package hk.org.ha.sc3.sybasechatops.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.transaction.Transactional;

import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroup;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroupMapping;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.db.StoreVariable;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CommandGroupService {

    private CommandGroupRepository commandGroupRepository;
    private CommandContext cmdContext;
    private List<ICommand> cmdServices;

    /* Constructor */
    public CommandGroupService(
            CommandGroupRepository commandGroupRepository, List<ICommand> cmdServices) {
        this.commandGroupRepository = commandGroupRepository;
        this.cmdContext = new CommandContext();
        this.cmdServices = cmdServices;
    }

    @Transactional
    public List<CommandResult> execByCommandGroupId(String commandGroupId, String hostName, String instanceName,
            HashMap<String, String> storeVariable)
            throws Exception {
        CommandGroup cmdGrp = this.commandGroupRepository.findById(commandGroupId)
                .orElseThrow(() -> new RuntimeException("Command group not found"));
        cmdGrp.setHost(hostName);
        cmdGrp.setInstance(instanceName);

        return execGroup(cmdGrp, storeVariable);
    }

    public List<CommandResult> execGroup(CommandGroup commandGroup, HashMap<String, String> storeVariablesMap)
            throws Exception {
        List<CommandGroupMapping> mappings = commandGroup.getCmdGrpMappings();
        List<CommandResult> cmdResults = new ArrayList<>();

        for (CommandGroupMapping mapping : mappings) {
            Command cmd = mapping.getCommand();
            if (!cmd.isEnabled()) {
                break;
            }

            cmd.setStoreVariables(storeVariablesMap);

            for(ICommand command:cmdServices){
                if(cmd.getCmdType() == command.getCmdType()){
                    cmdContext.setCommandExecutor(command);
                }
            }

            // set icommand return
            CommandResult cmdResult = this.cmdContext.executeCommand(cmd);
            cmdResult.setPdfTitle(mapping.getPdfTitle());

            for (StoreVariable storeVariable : mapping.getStoreVariables()) {
                Optional<String> storeVariableValue = Optional.empty();
                Pattern pattern = Pattern.compile(storeVariable.getRegexpStr());

                Matcher matcher = pattern.matcher(cmdResult.getStdout());
                if (matcher.find()) {
                    storeVariableValue = Optional.of(matcher.group(1));
                }

                if (storeVariable.isMandatory()) {
                    storeVariablesMap.put(storeVariable.getName(),
                            storeVariableValue.orElseThrow(() -> new RuntimeException(
                                    "Cannot find the target store variable of pattern "
                                            + storeVariable.getRegexpStr())));
                }
            }

            cmdResults.add(cmdResult);
        }
        return cmdResults;
    }

}
