package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_global_allowed_db", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class GlobalAllowedDB {
    
    @Id
    @Enumerated(EnumType.STRING)
    private DatabaseTypeEnum databaseType;
}