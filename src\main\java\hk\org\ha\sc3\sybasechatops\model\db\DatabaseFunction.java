package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import hk.org.ha.sc3.sybasechatops.model.db.id.DatabaseFunctionId;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "database_function", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class DatabaseFunction {
    @EmbeddedId
    private DatabaseFunctionId id;
}
