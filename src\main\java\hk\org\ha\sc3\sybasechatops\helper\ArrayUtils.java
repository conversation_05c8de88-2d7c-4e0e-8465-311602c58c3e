package hk.org.ha.sc3.sybasechatops.helper;

import java.util.Arrays;
import java.util.stream.IntStream;

public class ArrayUtils {
    public static int getIndex(String[] array, String search) {
        return IntStream.range(0, array.length)
                .filter(i -> array[i].equals(search))
                .findFirst().orElse(-1);
    }

    public static <T> T[] append(T[] arr, T element) {
        final int N = arr.length;
        arr = Arrays.copyOf(arr, N + 1);
        arr[N] = element;
        return arr;
    }
}
