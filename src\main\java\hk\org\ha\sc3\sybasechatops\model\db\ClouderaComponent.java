package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import hk.org.ha.sc3.sybasechatops.constant.ClouderaRoleEnum;
import hk.org.ha.sc3.sybasechatops.model.db.id.DatabaseId;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_cloudera_component", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class ClouderaComponent {
    @EmbeddedId
    private DatabaseId id;

    @Enumerated(EnumType.STRING)
    private ClouderaRoleEnum role;

    @ManyToOne
    @JoinColumn(name = "clusterId", referencedColumnName = "clusterId")
    ClouderaCluster cluster;
}
