package hk.org.ha.sc3.sybasechatops.controller;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckReturn;
import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.PDFService;

@RestController
@RequestMapping("/info")
public class DbInfoController {

    private PDFService pdfService;
    private DatabaseRepository databaseRepository;

    public DbInfoController(PDFService pdfService, DatabaseRepository databaseRepository) {
        this.pdfService = pdfService;
        this.databaseRepository = databaseRepository;
    }

    @PostMapping("instances/pdf")
    public ChatopsRespBase instancesPdf(@RequestBody ChatopsReq requestBody) throws IOException {
        String[] keywords = requestBody.getKeyword().split("\\|");
        String instanceWildcast = keywords[0];
        DatabaseTypeEnum type = DatabaseTypeEnum.valueOf(keywords[1]);
        String requester = requestBody.getRequester();
        Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

        List<Database> databases = this.databaseRepository.findByTypeAndIdInstanceLikeIgnoreCase(type,
                instanceWildcast);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
        String fileName = String.format("instance_list(%s)_%s.pdf", instanceWildcast, dateTime.format(formatter));
        pdfService.generateInstanceList(fileName, requester, dateTime, databases);

        String base64 = this.pdfService.generateBase64FromPdf(fileName);

        HealthCheckReturn hcReturn = HealthCheckReturn.builder().statusCode(200).file_output_name(fileName)
                .msg(String.format("Here is the instance list for wildcast search (%s)",
                        instanceWildcast))
                .pdfResult(base64)
                .build();
        return hcReturn;
    }
}
