meta {
  name: 6 level (approve)
  type: http
  seq: 6
}

post {
  url: http://localhost:8080/chatops/v2/menu
  body: json
  auth: none
}

body:json {
  {
    "msgChain": "@itbot|SC3 Database Health Check|{{session_id}}|CLOUDERA|CLOUDERA_API_CHK_ROLE|ARG_CDP_CLUSTER|ARG_CDP_SERVICE|Approve|Process",
    "requester": "Bon LAI",
    "corpID": "lkp625",
    "keyword": "{\"ARG_CDP_SERVICE\":\"HIVE_ON_TEZ\"}",
    "chatopsEnv": "{{env}}",
    "roomID": "{{room_id}}",
    "token": "{{chatops_token}}"
  }
}
