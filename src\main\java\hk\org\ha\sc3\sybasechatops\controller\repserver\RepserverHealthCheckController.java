package hk.org.ha.sc3.sybasechatops.controller.repserver;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import org.apache.sshd.common.SshException;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseHealthCheckController;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckReturn;
import hk.org.ha.sc3.sybasechatops.model.PdfContent;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.CommandGroupService;
import hk.org.ha.sc3.sybasechatops.service.PDFService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/repserver/health_check")
public class RepserverHealthCheckController extends BaseHealthCheckController {

        private CommandGroupService commandGroupService;

        /* A constructor to accept all private fields */
        public RepserverHealthCheckController(PDFService pdfService,
                        DatabaseRepository databaseRepository, CommandGroupService commandGroupService) {
                super(pdfService, databaseRepository, commandGroupService);
                this.commandGroupService = commandGroupService;
        }

        public List<CommandResult> getHealthCheckContentWithDbsub(String hostName, String instanceName,
                        String commandGroupId, String rds, String rdb)
                        throws Exception {
                HashMap<String, String> map = new HashMap<>();
                map.put("rds", rds);
                map.put("rdb", rdb);
                List<CommandResult> results = this.commandGroupService.execByCommandGroupId(commandGroupId, hostName,
                                instanceName,
                                map);
                return results;
        }

        public List<CommandResult> getHealthCheckContentWithFreeText(String hostName, String instanceName,
                        String commandGroupId, String freeText)
                        throws Exception {
                HashMap<String, String> map = new HashMap<>();
                map.put("free_text", freeText);
                List<CommandResult> results = this.commandGroupService.execByCommandGroupId(commandGroupId, hostName,
                                instanceName,
                                map);
                return results;
        }

        public List<CommandResult> getHealthCheckContentWithDbsubFreeText(String hostName, String instanceName,
                        String commandGroupId, String rds, String rdb, String freeText)
                        throws Exception {
                HashMap<String, String> map = new HashMap<>();

                map.put("rds", rds);
                map.put("rdb", rdb);
                map.put("free_text", freeText);
                List<CommandResult> results = this.commandGroupService.execByCommandGroupId(commandGroupId, hostName,
                                instanceName,
                                map);
                return results;
        }

        @Override
        public DatabaseTypeEnum getType() {
                return DatabaseTypeEnum.REPSERVER;
        }

        @PostMapping("dbsub")
        public ChatopsRespBase dbRepHealthCheck(@RequestBody ChatopsReq requestBody) throws IOException {
                try {
                        HealthCheckReturn hcReturn;
                        log.debug("Enter {} health check", getType());
                        String keywordArr = requestBody.getKeyword();
                        String[] keywords = keywordArr.split("\\|");

                        String commandAndArg = keywords[1];
                        String[] commandAndArgArr = commandAndArg.split("@");
                        String command = commandAndArgArr[0];
                        String rds = commandAndArgArr[1];
                        String rdb = commandAndArgArr[2];

                        String hostsAndInstancesStr = keywords[0];
                        String[] hostsAndInstancesArr = hostsAndInstancesStr.split("\\/");

                        // HashMap<String, String[]> hostInstancesMap = new HashMap<>();
                        MultiValueMap<String, String[]> hostInstancesMap = new LinkedMultiValueMap<>();
                        for (String hostAndInstancesStr : hostsAndInstancesArr) {
                                String[] hostAndInstancesSplit = hostAndInstancesStr.split(":");
                                String host = hostAndInstancesSplit[0];
                                String instancesStr = hostAndInstancesSplit[1];
                                String[] instances = instancesStr.split(",");
                                hostInstancesMap.add(host, instances);
                        }

                        String requester = requestBody.getRequester();
                        Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
                        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

                        List<PdfContent> pdfContents = new ArrayList<>();

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
                        String fileName = String.format("%s.pdf", dateTime.format(formatter));

                        for (Entry<String, List<String[]>> entry : hostInstancesMap.entrySet()) {
                                String hostName = entry.getKey();
                                for (String[] instanceNameArr : entry.getValue()) {
                                        for (String instanceName : instanceNameArr) {
                                                log.info("{} health checking. Instance {}. Host {}. Command {}. RDS {}. RDB {}.",
                                                                getType(),
                                                                instanceName, hostName, command, rds, rdb);

                                                long hcStartTime = System.currentTimeMillis();
                                                log.debug("Start timestamp: {}", hcStartTime);

                                                List<CommandResult> healthCheckContent = getHealthCheckContentWithDbsub(
                                                                hostName,
                                                                instanceName,
                                                                command,
                                                                rds, rdb);

                                                // Mark elapse time
                                                long hcEndTime = System.currentTimeMillis();
                                                log.debug("End timestamp: {}", hcEndTime);
                                                double hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                                                PdfContent pdfContent = PdfContent.builder().hostName(hostName)
                                                                .instanceName(instanceName)
                                                                .healthCheckContent(healthCheckContent)
                                                                .hcElapsedTime(hcElapsedTime).build();
                                                pdfContents.add(pdfContent);

                                        }

                                }
                        }

                        pdfService.generateDbHealthCheckPdf(fileName, requester,
                                        dateTime,
                                        pdfContents);
                        String base64 = this.pdfService.generateBase64FromPdf(fileName);

                        log.debug("filename {}", fileName);
                        log.debug("base64 {}", base64);

                        hcReturn = HealthCheckReturn.builder().statusCode(200)
                                        .file_output_name(fileName)
                                        .msg(String.format("Here is the %s health check report",
                                                        getType()))
                                        .pdfResult(base64)
                                        .build();
                        return hcReturn;
                } catch (SshException e) {
                        log.error("ssh exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report when SSH. Error: "
                                                        + e.getMessage())
                                        .build();
                } catch (Exception e) {
                        log.error("General exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report. Error: "
                                                        + e.getMessage())
                                        .build();
                }

        }

        @PostMapping("freetext")
        public ChatopsRespBase dbRepHealthCheckFreetext(@RequestBody ChatopsReq requestBody) throws IOException {
                try {
                        HealthCheckReturn hcReturn;
                        log.debug("Enter {} health check", getType());
                        String keywordArr = requestBody.getKeyword();
                        String[] keywords = keywordArr.split("\\|");

                        String commandAndArg = keywords[1];
                        String[] commandAndArgArr = commandAndArg.split("@");
                        String command = commandAndArgArr[0];
                        String freeText = commandAndArgArr[1];

                        String hostsAndInstancesStr = keywords[0];
                        String[] hostsAndInstancesArr = hostsAndInstancesStr.split("\\/");

                        // HashMap<String, String[]> hostInstancesMap = new HashMap<>();
                        MultiValueMap<String, String[]> hostInstancesMap = new LinkedMultiValueMap<>();
                        for (String hostAndInstancesStr : hostsAndInstancesArr) {
                                String[] hostAndInstancesSplit = hostAndInstancesStr.split(":");
                                String host = hostAndInstancesSplit[0];
                                String instancesStr = hostAndInstancesSplit[1];
                                String[] instances = instancesStr.split(",");
                                hostInstancesMap.add(host, instances);
                        }

                        String requester = requestBody.getRequester();
                        Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
                        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

                        List<PdfContent> pdfContents = new ArrayList<>();

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
                        String fileName = String.format("%s.pdf", dateTime.format(formatter));

                        for (Entry<String, List<String[]>> entry : hostInstancesMap.entrySet()) {
                                String hostName = entry.getKey();
                                for (String[] instanceNameArr : entry.getValue()) {
                                        for (String instanceName : instanceNameArr) {
                                                log.info("{} health checking. Instance {}. Host {}. Command {}. Free text {}.",
                                                                getType(),
                                                                instanceName, hostName, command, freeText);

                                                long hcStartTime = System.currentTimeMillis();
                                                log.debug("Start timestamp: {}", hcStartTime);

                                                List<CommandResult> healthCheckContent = getHealthCheckContentWithFreeText(
                                                                hostName,
                                                                instanceName,
                                                                command,
                                                                freeText);

                                                // Mark elapse time
                                                long hcEndTime = System.currentTimeMillis();
                                                log.debug("End timestamp: {}", hcEndTime);
                                                double hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                                                PdfContent pdfContent = PdfContent.builder().hostName(hostName)
                                                                .instanceName(instanceName)
                                                                .healthCheckContent(healthCheckContent)
                                                                .hcElapsedTime(hcElapsedTime).build();
                                                pdfContents.add(pdfContent);

                                        }

                                }
                        }

                        pdfService.generateDbHealthCheckPdf(fileName, requester,
                                        dateTime,
                                        pdfContents);
                        String base64 = this.pdfService.generateBase64FromPdf(fileName);

                        log.debug("filename {}", fileName);
                        log.debug("base64 {}", base64);

                        hcReturn = HealthCheckReturn.builder().statusCode(200)
                                        .file_output_name(fileName)
                                        .msg(String.format("Here is the %s health check report",
                                                        getType()))
                                        .pdfResult(base64)
                                        .build();
                        return hcReturn;
                } catch (SshException e) {
                        log.error("ssh exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report when SSH. Error: "
                                                        + e.getMessage())
                                        .build();
                } catch (Exception e) {
                        log.error("General exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report. Error: "
                                                        + e.getMessage())
                                        .build();
                }

        }

        @PostMapping("dbsub/freetext")
        public ChatopsRespBase dbRepHealthCheckDbsubAndFreetext(@RequestBody ChatopsReq requestBody)
                        throws IOException {
                try {
                        HealthCheckReturn hcReturn;
                        log.debug("Enter {} health check", getType());
                        String keywordArr = requestBody.getKeyword();
                        String[] keywords = keywordArr.split("\\|");

                        String commandAndArg = keywords[1];
                        String[] commandAndArgArr = commandAndArg.split("@");
                        String command = commandAndArgArr[0];
                        String rds = commandAndArgArr[1];
                        String rdb = commandAndArgArr[2];
                        String freeText = commandAndArgArr[3];

                        String hostsAndInstancesStr = keywords[0];
                        String[] hostsAndInstancesArr = hostsAndInstancesStr.split("\\/");

                        // HashMap<String, String[]> hostInstancesMap = new HashMap<>();
                        MultiValueMap<String, String[]> hostInstancesMap = new LinkedMultiValueMap<>();
                        for (String hostAndInstancesStr : hostsAndInstancesArr) {
                                String[] hostAndInstancesSplit = hostAndInstancesStr.split(":");
                                String host = hostAndInstancesSplit[0];
                                String instancesStr = hostAndInstancesSplit[1];
                                String[] instances = instancesStr.split(",");
                                hostInstancesMap.add(host, instances);
                        }

                        String requester = requestBody.getRequester();
                        Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
                        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

                        List<PdfContent> pdfContents = new ArrayList<>();

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
                        String fileName = String.format("%s.pdf", dateTime.format(formatter));

                        for (Entry<String, List<String[]>> entry : hostInstancesMap.entrySet()) {
                                String hostName = entry.getKey();
                                for (String[] instanceNameArr : entry.getValue()) {
                                        for (String instanceName : instanceNameArr) {
                                                log.info("{} health checking. Instance {}. Host {}. Command {}. RDS {}. RDB {}. Free text {}.",
                                                                getType(),
                                                                instanceName, hostName, command, rds, rdb, freeText);

                                                long hcStartTime = System.currentTimeMillis();
                                                log.debug("Start timestamp: {}", hcStartTime);

                                                List<CommandResult> healthCheckContent = getHealthCheckContentWithDbsubFreeText(
                                                                hostName,
                                                                instanceName,
                                                                command,
                                                                rds,
                                                                rdb,
                                                                freeText);

                                                // Mark elapse time
                                                long hcEndTime = System.currentTimeMillis();
                                                log.debug("End timestamp: {}", hcEndTime);
                                                double hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                                                PdfContent pdfContent = PdfContent.builder().hostName(hostName)
                                                                .instanceName(instanceName)
                                                                .healthCheckContent(healthCheckContent)
                                                                .hcElapsedTime(hcElapsedTime).build();
                                                pdfContents.add(pdfContent);

                                        }

                                }
                        }

                        pdfService.generateDbHealthCheckPdf(fileName, requester,
                                        dateTime,
                                        pdfContents);
                        String base64 = this.pdfService.generateBase64FromPdf(fileName);

                        log.debug("filename {}", fileName);
                        log.debug("base64 {}", base64);

                        hcReturn = HealthCheckReturn.builder().statusCode(200)
                                        .file_output_name(fileName)
                                        .msg(String.format("Here is the %s health check report",
                                                        getType()))
                                        .pdfResult(base64)
                                        .build();
                        return hcReturn;
                } catch (SshException e) {
                        log.error("ssh exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report when SSH. Error: "
                                                        + e.getMessage())
                                        .build();
                } catch (Exception e) {
                        log.error("General exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report. Error: "
                                                        + e.getMessage())
                                        .build();
                }

        }

}
