package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsChatroom;
import hk.org.ha.sc3.sybasechatops.repository.ChatroomRepository;
import hk.org.ha.sc3.sybasechatops.service.JasyptEncryptionService;
import hk.org.ha.sc3.sybasechatops.service.TokenService;

/**
 * Unit tests for the TokenService class
 * Uses <PERSON><PERSON><PERSON> to mock dependencies
 */
@ExtendWith(MockitoExtension.class)
public class TokenServiceTest {

    @Mock
    private JasyptEncryptionService encryptionService;

    @Mock
    private ChatroomRepository chatroomRepository;

    @InjectMocks
    private TokenService tokenService;

    private static final String TEST_TOKEN = "test-token-123";
    private static final String TEST_HASH = "abcd1234567890abcd1234567890abcd1234567890abcd1234567890abcd1234";
    private static final String TEST_ROOM_ID = "test-room-id";

    @BeforeEach
    void setUp() {
        // Reset mocks before each test
        reset(encryptionService, chatroomRepository);
    }

    @Test
    void testValidateToken_PlaintextTokenExists() {
        // Arrange
        when(chatroomRepository.existsByToken(TEST_TOKEN)).thenReturn(true);

        // Act
        boolean result = tokenService.validateToken(TEST_TOKEN);

        // Assert
        assertTrue(result);
        verify(chatroomRepository).existsByToken(TEST_TOKEN);
        // Should not try hashing if plaintext exists
        verify(encryptionService, never()).hashToken(any());
    }

    @Test
    void testValidateToken_HashedTokenExists() {
        // Arrange
        when(chatroomRepository.existsByToken(TEST_TOKEN)).thenReturn(false);
        when(encryptionService.hashToken(TEST_TOKEN)).thenReturn(TEST_HASH);
        when(chatroomRepository.existsByToken(TEST_HASH)).thenReturn(true);

        // Act
        boolean result = tokenService.validateToken(TEST_TOKEN);

        // Assert
        assertTrue(result);
        verify(chatroomRepository).existsByToken(TEST_TOKEN);
        verify(encryptionService).hashToken(TEST_TOKEN);
        verify(chatroomRepository).existsByToken(TEST_HASH);
    }

    @Test
    void testValidateToken_TokenNotFound() {
        // Arrange
        when(chatroomRepository.existsByToken(TEST_TOKEN)).thenReturn(false);
        when(encryptionService.hashToken(TEST_TOKEN)).thenReturn(TEST_HASH);
        when(chatroomRepository.existsByToken(TEST_HASH)).thenReturn(false);

        // Act
        boolean result = tokenService.validateToken(TEST_TOKEN);

        // Assert
        assertFalse(result);
        verify(chatroomRepository).existsByToken(TEST_TOKEN);
        verify(encryptionService).hashToken(TEST_TOKEN);
        verify(chatroomRepository).existsByToken(TEST_HASH);
    }

    @Test
    void testValidateToken_NullToken() {
        // Act
        boolean result = tokenService.validateToken(null);

        // Assert
        assertFalse(result);
        verify(chatroomRepository, never()).existsByToken(any());
        verify(encryptionService, never()).hashToken(any());
    }

    @Test
    void testValidateToken_EmptyToken() {
        // Act
        boolean result = tokenService.validateToken("");

        // Assert
        assertFalse(result);
        verify(chatroomRepository, never()).existsByToken(any());
        verify(encryptionService, never()).hashToken(any());
    }

    @Test
    void testValidateTokenForRoom_PlaintextTokenExists() {
        // Arrange
        ChatopsChatroom chatroom = createTestChatroom();
        when(chatroomRepository.findByTokenAndRoomId(TEST_TOKEN, TEST_ROOM_ID))
            .thenReturn(Optional.of(chatroom));

        // Act
        boolean result = tokenService.validateTokenForRoom(TEST_TOKEN, TEST_ROOM_ID);

        // Assert
        assertTrue(result);
        verify(chatroomRepository).findByTokenAndRoomId(TEST_TOKEN, TEST_ROOM_ID);
        verify(encryptionService, never()).hashToken(any());
    }

    @Test
    void testValidateTokenForRoom_HashedTokenExists() {
        // Arrange
        ChatopsChatroom chatroom = createTestChatroom();
        when(chatroomRepository.findByTokenAndRoomId(TEST_TOKEN, TEST_ROOM_ID))
            .thenReturn(Optional.empty());
        when(encryptionService.hashToken(TEST_TOKEN)).thenReturn(TEST_HASH);
        when(chatroomRepository.findByTokenAndRoomId(TEST_HASH, TEST_ROOM_ID))
            .thenReturn(Optional.of(chatroom));

        // Act
        boolean result = tokenService.validateTokenForRoom(TEST_TOKEN, TEST_ROOM_ID);

        // Assert
        assertTrue(result);
        verify(chatroomRepository).findByTokenAndRoomId(TEST_TOKEN, TEST_ROOM_ID);
        verify(encryptionService).hashToken(TEST_TOKEN);
        verify(chatroomRepository).findByTokenAndRoomId(TEST_HASH, TEST_ROOM_ID);
    }

    @Test
    void testHashToken() {
        // Arrange
        when(encryptionService.hashToken(TEST_TOKEN)).thenReturn(TEST_HASH);

        // Act
        String result = tokenService.hashToken(TEST_TOKEN);

        // Assert
        assertEquals(TEST_HASH, result);
        verify(encryptionService).hashToken(TEST_TOKEN);
    }

    @Test
    void testMigrateAllChatroomTokensToHash() {
        // Arrange
        ChatopsChatroom chatroom1 = createTestChatroom();
        chatroom1.setRoomId("room1");
        chatroom1.setToken("plaintext-token-1");

        ChatopsChatroom chatroom2 = createTestChatroom();
        chatroom2.setRoomId("room2");
        chatroom2.setToken(TEST_HASH); // already hashed

        List<ChatopsChatroom> allChatrooms = Arrays.asList(chatroom1, chatroom2);
        when(chatroomRepository.findAll()).thenReturn(allChatrooms);
        when(encryptionService.isTokenHash("plaintext-token-1")).thenReturn(false);
        when(encryptionService.isTokenHash(TEST_HASH)).thenReturn(true);
        when(encryptionService.hashToken("plaintext-token-1")).thenReturn("hashed-token-1");
        when(chatroomRepository.save(chatroom1)).thenReturn(chatroom1);

        // Act
        long result = tokenService.migrateAllChatroomTokensToHash();

        // Assert
        assertEquals(1, result); // Only one chatroom was migrated
        verify(chatroomRepository).findAll();
        verify(encryptionService).isTokenHash("plaintext-token-1");
        verify(encryptionService).isTokenHash(TEST_HASH);
        verify(encryptionService).hashToken("plaintext-token-1");
        verify(chatroomRepository).save(chatroom1);
        verify(chatroomRepository, never()).save(chatroom2); // Already hashed, no save needed
    }

    @Test
    void testIsTokenHashed() {
        // Arrange
        when(encryptionService.isTokenHash(TEST_HASH)).thenReturn(true);
        when(encryptionService.isTokenHash(TEST_TOKEN)).thenReturn(false);

        // Act & Assert
        assertTrue(tokenService.isTokenHashed(TEST_HASH));
        assertFalse(tokenService.isTokenHashed(TEST_TOKEN));
        
        verify(encryptionService).isTokenHash(TEST_HASH);
        verify(encryptionService).isTokenHash(TEST_TOKEN);
    }

    private ChatopsChatroom createTestChatroom() {
        ChatopsChatroom chatroom = new ChatopsChatroom();
        chatroom.setRoomId(TEST_ROOM_ID);
        chatroom.setToken(TEST_TOKEN);
        chatroom.setRoomName("Test Room");
        chatroom.setTeam("Test Team");
        return chatroom;
    }
}