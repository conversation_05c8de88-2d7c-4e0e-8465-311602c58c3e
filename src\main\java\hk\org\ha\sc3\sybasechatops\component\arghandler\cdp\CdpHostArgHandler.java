package hk.org.ha.sc3.sybasechatops.component.arghandler.cdp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.ClouderaRoleEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaComponent;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaComponentRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CdpHostArgHandler extends ICommandArgHandler {

    private ClouderaComponentRepository clouderaComponentRepository;

    public CdpHostArgHandler(ClouderaComponentRepository clouderaComponentRepository) {
        this.clouderaComponentRepository = clouderaComponentRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_CDP_HOST;
    }

    
    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        String cluster = (String) httpSession.getAttribute(CmdArgEnum.ARG_CDP_CLUSTER.name());
        String serviceText = (String) httpSession.getAttribute(CmdArgEnum.ARG_CDP_SERVICE.name());
        
        if (cluster != null && serviceText != null) {
            ClouderaRoleEnum service = ClouderaRoleEnum.valueOf(serviceText);
            List<ClouderaComponent> components = this.clouderaComponentRepository
                    .findDistinctByRoleAndClusterClusterName(service, cluster);

            components.stream().map(component -> component.getId()).forEach(id -> {
                hiddenValue.put(getCmdArgType().name(), id.getHost());
                hiddenValue.put(CmdArgEnum.ARG_CDP_INSTANCE.name(), id.getInstance());
                String jsonStr;
                try {
                    jsonStr = mapper.writeValueAsString(hiddenValue);
                    Option option = Option.builder()
                    .text(id.getHost())
                    .value(jsonStr)
                    .nextLabel(msgChain + "|" + getCmdArgType())
                    .build();
                options.add(option);
                } catch (JsonProcessingException e) {
                    log.error("Error processing JSON for service: {}", service, e);
                }
            });
        }

        return options;
    }
}
