package hk.org.ha.sc3.sybasechatops.model.db;

import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_command_argument", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class CommandArgument {
    @Id
    private String id;
    
    private String keyName;
    
    private String description;

    @Column(name = "required_para")
    private String requiredPara;

    @Enumerated(EnumType.STRING)
    @Column(name = "arg_type")
    private CmdArgEnum argType;

    @OneToMany(mappedBy = "commandArgument", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<CmdArgMapping> commandMappings;

}