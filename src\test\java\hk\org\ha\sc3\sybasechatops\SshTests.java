package hk.org.ha.sc3.sybasechatops;

import java.io.IOException;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.service.SshService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class SshTests {

	@Autowired
	private SshService sshService;

	@Test
	void testCommandNoTimeout() throws IOException {
		sshService.exec("oracle", "dbmoratst11d", "ls;sleep 15;ls;", null);
	}

	@Test
	void testCommandWithTimeout() throws IOException {
		sshService.exec("oracle", "dbmoratst11h", "ls;sleep 5;ls;", 6);
	}

}
