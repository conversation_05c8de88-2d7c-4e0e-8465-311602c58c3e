package hk.org.ha.sc3.sybasechatops;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import hk.org.ha.sc3.sybasechatops.model.PdfContent;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.PDFService;

/* To test pdfService class */

@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class PdfTests {

	@Autowired
	private PDFService pdfService;

	@Autowired
	private DatabaseRepository databaseRepository;

	@Test
	void sybaseGenerateDbHealthCheckPdf() {
		/* Test case for pdfService.generateDbHealthCheckPdf method */
		String pdfFile = "test_sybase.pdf";
		String ihcOutput = "Password:\n(T1_ASETEST_ST1) DB Status          : WARN\nDB login failure (timeout: 10s)     : NO\nCPU utilization >1%                 : WARN (syb_system_pool:20.00% syb_default_pool:6.15%)\nData Size >50%                      : WARN (corpopas_db:83.8% test_db1:98.0% atl_db:94.4% dbccdb:55.0%)\nDetails: /appl/dbs/dump/ase_ihc_portal/log/T1_ASETEST_ST1.20230627T163312.detail.log\nFull Log: /appl/dbs/dump/ase_ihc_portal/log/T1_ASETEST_ST1.20230627T163312.ase_ihc.out\n";
		List<CommandResult> results = new ArrayList<CommandResult>();
		CommandResult commandResult = CommandResult.builder().stdout(ihcOutput).stderr("").rc(0).pdfTitle("Result")
				.returnType(CmdReturnEnum.SSH).build();
		results.add(commandResult);
		String hostName = "abc_host";
		String instanceName = "T1_ASETEST_ST1";
		double hcElapsedTime = 123;
		List<PdfContent> pdfContents = new ArrayList<>();
		pdfContents.add(PdfContent.builder().hcElapsedTime(hcElapsedTime).healthCheckContent(results)
				.hostName(hostName)
				.instanceName(instanceName).build());
		pdfService.generateDbHealthCheckPdf(pdfFile, "Bon",
				LocalDateTime.now(), pdfContents);
	}

	@Test
	void generateDbHealthCheckPdf_VeryShortStdout() {
		/* Test case for pdfService.generateDbHealthCheckPdf method */
		String pdfFile = "test_short_output.pdf";
		String ihcOutput = "hahahaha";
		// String ihcOutput = "";
		List<CommandResult> results = new ArrayList<CommandResult>();
		CommandResult commandResult = CommandResult.builder().stdout(ihcOutput).stderr("").rc(0)
				.returnType(CmdReturnEnum.SSH).build();
		results.add(commandResult);

		String hostName = "abc_host";
		String instanceName = "T1_ASETEST_ST1";
		double hcElapsedTime = 123;
		List<PdfContent> pdfContents = new ArrayList<>();
		pdfContents.add(PdfContent.builder().hcElapsedTime(hcElapsedTime).healthCheckContent(results)
				.hostName(hostName)
				.instanceName(instanceName).build());
		pdfService.generateDbHealthCheckPdf(pdfFile, "Bon",
				LocalDateTime.now(), pdfContents);
	}

	@Test
	void testPdfColor() {
		/* Test case for pdfService.generateDbHealthCheckPdf method */
		String pdfFile = "test_ansi_color.pdf";
		String ihcOutput = "\u001B[0;34mInstances    Status DATABASE_ROLE       OPEN_MODE                SYNC    transport lag      apply lag          MRP           \n"
				+ //
				"------------ ------ ------------------- ------------------------ ------- ------------------ ------------------ ------------- \n"
				+ //
				"darc1p1      \u001B[1;32mUP    \u001B[m PRIMARY             READ WRITE               N/A     N/A                N/A                N/A          ";
		List<CommandResult> results = new ArrayList<CommandResult>();
		CommandResult commandResult = CommandResult.builder().stdout(ihcOutput).stderr("").rc(0)
				.pdfTitle("Color result").returnType(CmdReturnEnum.SSH).build();
		results.add(commandResult);
		String hostName = "abc_host";
		String instanceName = "T1_ASETEST_ST1";
		double hcElapsedTime = 123;
		List<PdfContent> pdfContents = new ArrayList<>();
		pdfContents.add(PdfContent.builder().hcElapsedTime(hcElapsedTime).healthCheckContent(results)
				.hostName(hostName)
				.instanceName(instanceName).build());
		pdfService.generateDbHealthCheckPdf(pdfFile, "Bon",
				LocalDateTime.now(), pdfContents);
	}

	@Test
	void generateInstanceList() {
		String pdfFile = "database_list.pdf";
		List<Database> databases = databaseRepository.findAll();
		pdfService.generateInstanceList(pdfFile, "Bon",
				LocalDateTime.now(), databases);
	}


	@Test
	void testGrafanaCapture() {
		/* Test case for pdfService.generateDbHealthCheckPdf method */
		String pdfFile = "test_grafana_capture.pdf";
		String ihcOutput = "\u001B[0;34mInstances    Status DATABASE_ROLE       OPEN_MODE                SYNC    transport lag      apply lag          MRP           \n"
				+ //
				"------------ ------ ------------------- ------------------------ ------- ------------------ ------------------ ------------- \n"
				+ //
				"darc1p1      \u001B[1;32mUP    \u001B[m PRIMARY             READ WRITE               N/A     N/A                N/A                N/A          ";
		List<CommandResult> results = new ArrayList<CommandResult>();
		CommandResult commandResult = CommandResult.builder().stdout(ihcOutput).stderr("").rc(0)
				.pdfTitle("Grafana result").file("./screenshot/2024_08_13_14_59_03_256.png")
				.returnType(CmdReturnEnum.SSH).build();
		results.add(commandResult);
		String hostName = "abc_host";
		String instanceName = "T1_ASETEST_ST1";
		double hcElapsedTime = 123;
		List<PdfContent> pdfContents = new ArrayList<>();
		pdfContents.add(PdfContent.builder().hcElapsedTime(hcElapsedTime).healthCheckContent(results)
				.hostName(hostName)
				.instanceName(instanceName).build());
		pdfService.generateDbHealthCheckPdf(pdfFile, "Bon",
				LocalDateTime.now(), pdfContents);
	}

	@Test
	void testPdfWithSessionAttributes() {
		/* Test case for pdfService.generateDbHealthCheckPdf method with session attributes */
		String pdfFile = "test_session_attributes_v2.pdf";
		String ihcOutput = "Test output with session attributes included in PDF header";
		List<CommandResult> results = new ArrayList<CommandResult>();
		CommandResult commandResult = CommandResult.builder().stdout(ihcOutput).stderr("").rc(0)
				.pdfTitle("Session Attributes Test").returnType(CmdReturnEnum.SSH).build();
		results.add(commandResult);
		String hostName = "test_host";
		String instanceName = "TEST_INSTANCE";
		double hcElapsedTime = 45.5;
		List<PdfContent> pdfContents = new ArrayList<>();
		pdfContents.add(PdfContent.builder().hcElapsedTime(hcElapsedTime).healthCheckContent(results)
				.hostName(hostName)
				.instanceName(instanceName).build());

		// Create session attributes map
		HashMap<String, String> sessionAttributes = new HashMap<>();
		sessionAttributes.put("ROOM_ID", "room123");
		sessionAttributes.put("DB_TYPE", "SYBASE");
		sessionAttributes.put("TEAM", "SC3_TEAM");
		sessionAttributes.put("PRODUCT", "CHATOPS");
		sessionAttributes.put("CMD_GRP_ID", "SYBASE_HEALTH_CHECK");

		pdfService.generateDbHealthCheckPdf(pdfFile, "TestUser",
				LocalDateTime.now(), pdfContents, sessionAttributes);
	}
}
