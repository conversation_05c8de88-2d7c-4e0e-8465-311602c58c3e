package hk.org.ha.sc3.sybasechatops.service;

import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.repository.CyberarkAccountRepository;
import hk.org.ha.sc3.sybasechatops.repository.OsPasswordRepository;

@Service
public class OsPasswordService {
    private OsPasswordRepository osPasswordRepository;
    private CyberarkService cyberarkService;
    private CyberarkAccountRepository cyberarkAccountRepository;

    public OsPasswordService(OsPasswordRepository osPasswordRepository, CyberarkService cyberarkService,
            CyberarkAccountRepository cyberarkAccountRepository) {
        this.osPasswordRepository = osPasswordRepository;
        this.cyberarkService = cyberarkService;
        this.cyberarkAccountRepository = cyberarkAccountRepository;
    }

    public String getPassword(String username, String host) {
        if (cyberarkAccountRepository.findByUserAndHost(username, host).isEmpty()) {
            return this.osPasswordRepository.findAll().get(0).getPassword();
        } else {
            return this.cyberarkService.getAccountPassword(username, host).block();
        }
    }
}
