package hk.org.ha.sc3.sybasechatops.repository;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsChatroom;

@Repository
public interface ChatroomRepository extends JpaRepository<ChatopsChatroom, String> {
    boolean existsByToken(String token);
    Optional<ChatopsChatroom> findByTokenAndRoomId(String token, String roomId);
    Optional<ChatopsChatroom> findByRoomId(String roomId);
}