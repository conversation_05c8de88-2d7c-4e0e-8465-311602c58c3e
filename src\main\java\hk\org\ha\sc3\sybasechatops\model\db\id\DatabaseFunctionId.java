package hk.org.ha.sc3.sybasechatops.model.db.id;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;

import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.model.db.Function;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Embeddable
@Getter
@Setter
@EqualsAndHashCode
public class DatabaseFunctionId implements Serializable {
    @ManyToOne
    @JoinColumns({
            @JoinColumn(name = "host", referencedColumnName = "host"),
            @JoinColumn(name = "instance", referencedColumnName = "instance")
    })
    Database database;

    @ManyToOne
    @JoinColumn(name = "function",referencedColumnName = "abbr")
    Function function;

}
