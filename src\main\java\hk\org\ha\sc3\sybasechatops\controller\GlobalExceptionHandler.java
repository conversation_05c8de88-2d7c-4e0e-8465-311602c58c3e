package hk.org.ha.sc3.sybasechatops.controller;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.Reply;
import lombok.extern.slf4j.Slf4j;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ResponseBody
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public ChatopsRespBase handleException(Exception e) {
        log.error("Global exception handler", e);
        return Reply.builder().statusCode(500)
                .msg("Failed to get the health check report. Error: "
                        + e.getMessage())
                .build();
    }
}
