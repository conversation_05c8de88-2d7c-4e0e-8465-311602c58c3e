package hk.org.ha.sc3.sybasechatops.component.arghandler.common;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.SessionAttrEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;

@ExtendWith(MockitoExtension.class)
class HostArgHandlerTest {

    @Mock
    private DatabaseRepository databaseRepository;

    @Mock
    private HttpSession httpSession;

    @InjectMocks
    private HostArgHandler hostArgHandler;

    private CommandArgument commandArgument;
    private String msgChain;

    @BeforeEach
    void setUp() {
        commandArgument = new CommandArgument();
        commandArgument.setDescription("Select a host");
        msgChain = "@itbot|SC3 Database Health Check|{{session_id}}|ORACLE|{{command}}";
    }

    @Test
    void shouldReturnCorrectCmdArgType() {
        // When
        CmdArgEnum result = hostArgHandler.getCmdArgType();

        // Then
        assertEquals(CmdArgEnum.ARG_HOST, result);
    }

    @Test
    void shouldReturnOptionsWhenTeamAndDbTypeExist() throws JsonProcessingException {
        // Given
        String team = "TEST_TEAM";
        String dbTypeStr = "ORACLE";
        DatabaseTypeEnum dbType = DatabaseTypeEnum.ORACLE;
        List<String> hosts = Arrays.asList("host1.example.com", "host2.example.com", "host3.example.com");

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(dbTypeStr);
        when(databaseRepository.findDistinctHostByTeamAndType(team, dbType)).thenReturn(hosts);

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertEquals(3, options.size());
        
        // Verify first option
        Option firstOption = options.get(0);
        assertEquals("host1.example.com", firstOption.getText());
        assertEquals(msgChain + "|" + CmdArgEnum.ARG_HOST, firstOption.getNextLabel());
        
        // Verify JSON value contains the host
        ObjectMapper mapper = new ObjectMapper();
        String jsonValue = firstOption.getValue();
        assertTrue(jsonValue.contains("host1.example.com"));
        assertTrue(jsonValue.contains(CmdArgEnum.ARG_HOST.name()));

        // Verify all hosts are included
        assertEquals("host1.example.com", options.get(0).getText());
        assertEquals("host2.example.com", options.get(1).getText());
        assertEquals("host3.example.com", options.get(2).getText());
    }

    @Test
    void shouldReturnOptionsWhenTeamExistsButDbTypeIsNull() {
        // Given
        String team = "TEST_TEAM";
        List<String> hosts = Arrays.asList("host1.example.com", "host2.example.com");

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(null);
        when(databaseRepository.findDistinctHostByTeamAndType(team, null)).thenReturn(hosts);

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertEquals(2, options.size());
        verify(databaseRepository).findDistinctHostByTeamAndType(team, null);
    }

    @Test
    void shouldHandleDifferentDatabaseTypes() {
        // Given
        String team = "TEST_TEAM";
        String dbTypeStr = "ASE";
        DatabaseTypeEnum dbType = DatabaseTypeEnum.ASE;
        List<String> hosts = Arrays.asList("ase-host1.example.com");

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(dbTypeStr);
        when(databaseRepository.findDistinctHostByTeamAndType(team, dbType)).thenReturn(hosts);

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertEquals(1, options.size());
        assertEquals("ase-host1.example.com", options.get(0).getText());
        verify(databaseRepository).findDistinctHostByTeamAndType(team, dbType);
    }

    @Test
    void shouldHandleLowercaseDbType() {
        // Given
        String team = "TEST_TEAM";
        String dbTypeStr = "oracle"; // lowercase
        DatabaseTypeEnum dbType = DatabaseTypeEnum.ORACLE;
        List<String> hosts = Arrays.asList("oracle-host.example.com");

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(dbTypeStr);
        when(databaseRepository.findDistinctHostByTeamAndType(team, dbType)).thenReturn(hosts);

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertEquals(1, options.size());
        verify(databaseRepository).findDistinctHostByTeamAndType(team, dbType);
    }

    @Test
    void shouldReturnEmptyListWhenTeamIsNull() {
        // Given
        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(null);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn("ORACLE");

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertTrue(options.isEmpty());
        verify(databaseRepository, never()).findDistinctHostByTeamAndType(any(), any());
    }

    @Test
    void shouldReturnEmptyListWhenNoHostsFound() {
        // Given
        String team = "TEST_TEAM";
        String dbTypeStr = "ORACLE";
        DatabaseTypeEnum dbType = DatabaseTypeEnum.ORACLE;

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(dbTypeStr);
        when(databaseRepository.findDistinctHostByTeamAndType(team, dbType)).thenReturn(Arrays.asList());

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertTrue(options.isEmpty());
        verify(databaseRepository).findDistinctHostByTeamAndType(team, dbType);
    }

    @Test
    void shouldHandleInvalidDbTypeGracefully() {
        // Given
        String team = "TEST_TEAM";
        String invalidDbType = "INVALID_DB_TYPE";

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(invalidDbType);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            hostArgHandler.getOptions(commandArgument, httpSession, msgChain);
        });
    }

    @Test
    void shouldVerifyJsonStructureInOptionValue() throws JsonProcessingException {
        // Given
        String team = "TEST_TEAM";
        String dbTypeStr = "ORACLE";
        DatabaseTypeEnum dbType = DatabaseTypeEnum.ORACLE;
        List<String> hosts = Arrays.asList("test-host.example.com");

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(dbTypeStr);
        when(databaseRepository.findDistinctHostByTeamAndType(team, dbType)).thenReturn(hosts);

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertEquals(1, options.size());
        Option option = options.get(0);
        
        // Parse the JSON value to verify structure
        ObjectMapper mapper = new ObjectMapper();
        @SuppressWarnings("unchecked")
        java.util.Map<String, String> hiddenValue = mapper.readValue(option.getValue(), java.util.Map.class);
        
        assertEquals("test-host.example.com", hiddenValue.get(CmdArgEnum.ARG_HOST.name()));
        assertEquals(1, hiddenValue.size()); // Should only contain the host entry
    }

    @Test
    void shouldHandleMultipleSessionAttributes() {
        // Given
        String team = "TEST_TEAM";
        String dbTypeStr = "MSSQL";
        DatabaseTypeEnum dbType = DatabaseTypeEnum.MSSQL;
        List<String> hosts = Arrays.asList("mssql-host.example.com");

        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn(team);
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn(dbTypeStr);
        when(databaseRepository.findDistinctHostByTeamAndType(team, dbType)).thenReturn(hosts);

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertEquals(1, options.size());
        verify(httpSession).getAttribute(SessionAttrEnum.TEAM.name());
        verify(httpSession).getAttribute(SessionAttrEnum.DB_TYPE.name());
        verify(databaseRepository).findDistinctHostByTeamAndType(team, dbType);
    }

    @Test
    void shouldHandleEmptyStringTeam() {
        // Given
        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn("");
        when(httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name())).thenReturn("ORACLE");

        // When
        List<Option> options = hostArgHandler.getOptions(commandArgument, httpSession, msgChain);

        // Then
        assertTrue(options.isEmpty());
        verify(databaseRepository, never()).findDistinctHostByTeamAndType(any(), any());
    }
}
