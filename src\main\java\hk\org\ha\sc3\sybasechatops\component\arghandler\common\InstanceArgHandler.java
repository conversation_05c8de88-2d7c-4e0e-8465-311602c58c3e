package hk.org.ha.sc3.sybasechatops.component.arghandler.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.SessionAttrEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class InstanceArgHandler extends ICommandArgHandler {

    private DatabaseRepository databaseRepository;

    public InstanceArgHandler(DatabaseRepository databaseRepository) {
        this.databaseRepository = databaseRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_INSTANCE;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        String team = (String) httpSession.getAttribute(SessionAttrEnum.TEAM.name());
        String host = (String) httpSession.getAttribute(CmdArgEnum.ARG_HOST.name());
        String dbTypeStr = (String) httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name());

        DatabaseTypeEnum dbType = dbTypeStr != null ? DatabaseTypeEnum.valueOf(dbTypeStr.toUpperCase()) : null;

        boolean isIncludeHost = false;
        List<Database> databases;
        if (host != null) {
            databases = databaseRepository.findByTypeAndIdHost(dbType, host);
        } else {
            databases = databaseRepository.findByTypeAndTeam(dbType, team);
            isIncludeHost = true;
        }

        for (Database db : databases) {
            hiddenValue.put(CmdArgEnum.ARG_HOST.name(), db.getId().getHost());
            hiddenValue.put(CmdArgEnum.ARG_INSTANCE.name(), db.getId().getInstance());

            String buttonText = db.getId().getInstance();
            if (isIncludeHost) {
                buttonText = db.getId().getHost() + "|" + db.getId().getInstance();
            }

            try {
                Option option = Option.builder().text(buttonText)
                        .value(mapper.writeValueAsString(hiddenValue))
                        .nextLabel(msgChain + "|" + getCmdArgType()).build();
                options.add(option);
            } catch (JsonProcessingException e) {
                    log.error("Error processing JSON for host: {}", host, e);
            }
        }

        return options;
    }
}
