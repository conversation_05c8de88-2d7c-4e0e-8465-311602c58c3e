package hk.org.ha.sc3.sybasechatops.controller.sybase;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.config.NotificationConfig;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseMenuController;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.TextInput;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;

@RestController
@RequestMapping("sybase")
public class SybaseMenuController extends BaseMenuController {

        private final TextInput sybaseInstanceInput;

        public SybaseMenuController(DatabaseRepository databaseRepository,
                        CommandGroupRepository commandGroupRepository, NotificationConfig notificationConfig) {
                super(databaseRepository, commandGroupRepository, SybaseHealthCheckController.class,
                                notificationConfig);
                sybaseInstanceInput = TextInput.builder()
                                .msg("Please input Sybase instance name wildcast (e.g. %T1_ASE%)")
                                .store("{ase_instance}").nextLabel("@itbot|Sybase|Instance List").build();
        }

        @Override
        public DatabaseTypeEnum getType() {
                return DatabaseTypeEnum.ASE;
        }

        @Override
        public ChatopsRespBase getBaseResponse(String msgChain, String token) {
                switch (msgChain) {
                        case "@itbot|Sybase":
                                return this.sybaseInstanceInput;
                        default:
                                return super.getUnknownReply();
                }
        }
}
