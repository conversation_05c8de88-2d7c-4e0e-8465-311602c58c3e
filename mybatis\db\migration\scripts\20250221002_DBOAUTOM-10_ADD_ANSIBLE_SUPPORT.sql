-- Description: Add support for Ansible commands in ChatOps

-- Add new table to store Ansible inventory
CREATE TABLE `health_check`.`chatops_ansible_inventory` (
  `id` INT NOT NULL,
  `hostname` VARCHAR(50) NOT NULL,
  PRIMARY KEY (`id`)
);


-- Add chatops menu button
INSERT INTO `health_check`.`chatops_command_group` (`id`, `cmd_type`, `db_type`, `enabled`) VALUES
('CIMS_HEALTH_CHECK', 'HOST', 'ORACLE', 1);

INSERT INTO `health_check`.`chatops_command` (`id`, `command`, `enabled`, `user`, `timeout`, `cmd_type`, `return_type`) VALUES
('ORA_CIMS_HEALTH_CHECK', 'odc-ora-cims-healthcheck', 1, NULL, NULL, 'ANSIBLE', 'WEBHOOK');


INSERT INTO `health_check`.`chatops_command_group_mapping` (`id`,`command_id`, `command_group_id`, `pdf_title`, `wait_for_id`, `exec_mode`, `sequence`) VALUES
(35, 'ORA_CIMS_HEALTH_CHECK', 'CIMS_HEALTH_CHECK', 'CIMS health check result', NULL, NULL, 1);

-- //@UNDO
DROP TABLE `health_check`.`chatops_ansible_inventory`;

DELETE FROM `health_check`.`chatops_command_group_mapping` WHERE `id` = 35;
DELETE FROM `health_check`.`chatops_command_group` WHERE `id` = 'CIMS_HEALTH_CHECK';
DELETE FROM `health_check`.`chatops_command` WHERE `id` = 'ORA_CIMS_HEALTH_CHECK';
