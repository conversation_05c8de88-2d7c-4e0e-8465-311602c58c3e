package hk.org.ha.sc3.sybasechatops.controller;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Optional;

import javax.servlet.http.HttpSession;

import org.apache.sshd.common.SshException;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.IDatabaseType;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckKeyword;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckKeywordItem;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckReturn;
import hk.org.ha.sc3.sybasechatops.model.PdfContent;
import hk.org.ha.sc3.sybasechatops.model.Reply;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.CommandGroupService;
import hk.org.ha.sc3.sybasechatops.service.PDFService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class BaseHealthCheckController implements IDatabaseType {
        protected PDFService pdfService;
        protected DatabaseRepository databaseRepository;
        protected CommandGroupService commandGroupService;

        /* A constructor to accept all private fields */
        public BaseHealthCheckController(PDFService pdfService, DatabaseRepository databaseRepository,
                        CommandGroupService commandGroupService) {
                this.pdfService = pdfService;
                this.databaseRepository = databaseRepository;
                this.commandGroupService = commandGroupService;
        }

        public List<CommandResult> getHealthCheckContent(String hostName, String instanceName,
                        String commandGroupId, HashMap<String, String> storeVariablesMap)
                        throws Exception {
                List<CommandResult> results = this.commandGroupService.execByCommandGroupId(commandGroupId, hostName,
                                instanceName, storeVariablesMap);
                return results;
        }

        @PostMapping
        public ChatopsRespBase dbHealthCheck(@RequestBody ChatopsReq requestBody, HttpSession httpSession) {
                String keyword = requestBody.getKeyword();
                try {
                        try {
                                requestBody.setKeyword(keyword);
                        } catch (Exception e) {
                                log.warn("Decryption failed, proceeding with original keyword. Error: {}",
                                                e.getMessage());
                        }
                        if (isValidJson(requestBody.getKeyword())) {
                                return dbHealthCheckV2(requestBody);
                        } else {
                                return dbHealthCheckV1(requestBody);
                        }
                } catch (Exception e) {
                        log.error("General exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report. Error: "
                                                        + e.getMessage())
                                        .build();
                }
        }

        public ChatopsRespBase dbHealthCheckV2(ChatopsReq requestBody) throws Exception {
                // To be implement: host and instance store in store variable map
                String keyword = requestBody.getKeyword();
                ObjectMapper objectMapper = new ObjectMapper();
                HealthCheckKeyword healthCheckKeyword = objectMapper.readValue(keyword,
                                new com.fasterxml.jackson.core.type.TypeReference<HealthCheckKeyword>() {
                                });
                HealthCheckReturn hcReturn;
                log.debug("Enter {} health check", getType());
                for (HealthCheckKeywordItem item : healthCheckKeyword.getItems()) {

                        String requester = requestBody.getRequester();
                        Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
                        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

                        List<PdfContent> pdfContents = new ArrayList<>();

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
                        String fileName = String.format("%s.pdf", dateTime.format(formatter));

                        log.info("{} health checking. Instance {}. Host {}. Command {}.",
                                        getType(),
                                        "null", "null", item.getCommandGroupId());

                        long hcStartTime = System.currentTimeMillis();
                        log.debug("Start timestamp: {}", hcStartTime);

                        List<CommandResult> healthCheckContent = getHealthCheckContent("null", "null",
                                        item.getCommandGroupId(), item.getStoreVariablesMap());

                        // Mark elapse time
                        long hcEndTime = System.currentTimeMillis();
                        log.debug("End timestamp: {}", hcEndTime);
                        double hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                        PdfContent pdfContent = PdfContent.builder().hostName("null")
                                        .instanceName("null")
                                        .healthCheckContent(healthCheckContent)
                                        .hcElapsedTime(hcElapsedTime).build();
                        pdfContents.add(pdfContent);

                        pdfService.generateDbHealthCheckPdf(fileName, requester,
                                        dateTime,
                                        pdfContents);
                        String base64 = this.pdfService.generateBase64FromPdf(fileName);

                        log.debug("filename {}", fileName);
                        log.debug("base64 {}", base64);

                        hcReturn = HealthCheckReturn.builder().statusCode(200)
                                        .file_output_name(fileName)
                                        .msg(String.format("Here is the %s health check report",
                                                        getType()))
                                        .pdfResult(base64)
                                        .build();
                        return hcReturn;

                }
                return Reply.builder().msg("Error in executing health check v2").statusCode(500).build();
        }

        public ChatopsRespBase dbHealthCheckV1(ChatopsReq requestBody) throws IOException {
                try {
                        /*
                         * keyword sample
                         * 
                         * {host_name}:{instance_name}[/{host_name}:{instance_name}...]|{command}
                         * 
                         * 1. "dbmoratst11d:dbsreps12|ORAROLE"
                         * 2. "dbmoratst11c:null|ORAINFO"
                         * 3. "dbmoratst11c:dbsreps11/dbmoratst11d:dbsreps12|ORAROLE"
                         * 
                         * 4. bdvmc2a:
                         */
                        HealthCheckReturn hcReturn;
                        log.debug("Enter {} health check", getType());
                        String keywordArr = requestBody.getKeyword();
                        String[] keywords = keywordArr.split("\\|");

                        String commandGroupId = keywords[1];

                        String hostsAndInstancesStr = keywords[0];
                        String[] hostsAndInstancesArr = hostsAndInstancesStr.split("\\/");

                        // HashMap<String, String[]> hostInstancesMap = new HashMap<>();
                        MultiValueMap<String, String[]> hostInstancesMap = new LinkedMultiValueMap<>();
                        for (String hostAndInstancesStr : hostsAndInstancesArr) {
                                String[] hostAndInstancesSplit = hostAndInstancesStr.split(":");
                                String host = hostAndInstancesSplit[0];
                                String instancesStr = hostAndInstancesSplit[1];
                                String[] instances = instancesStr.split(",");
                                hostInstancesMap.add(host, instances);
                        }

                        String requester = requestBody.getRequester();
                        Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
                        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

                        List<PdfContent> pdfContents = new ArrayList<>();

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
                        String fileName = String.format("%s.pdf", dateTime.format(formatter));

                        for (Entry<String, List<String[]>> entry : hostInstancesMap.entrySet()) {
                                String hostName = entry.getKey();
                                for (String[] instanceNameArr : entry.getValue()) {
                                        for (String instanceName : instanceNameArr) {
                                                log.info("{} health checking. Instance {}. Host {}. Command {}.",
                                                                getType(),
                                                                instanceName, hostName, commandGroupId);

                                                long hcStartTime = System.currentTimeMillis();
                                                log.debug("Start timestamp: {}", hcStartTime);

                                                HashMap<String, String> storeVariableMap = new HashMap<String, String>();
                                                storeVariableMap.put("roomId", requestBody.getRoomID());
                                                storeVariableMap.put("requester", requestBody.getRequester());
                                                List<CommandResult> healthCheckContent = getHealthCheckContent(
                                                                hostName,
                                                                instanceName,
                                                                commandGroupId, storeVariableMap);

                                                if (healthCheckContent.size()==1 & healthCheckContent.get(0).getReturnType().equals(CmdReturnEnum.WEBHOOK)){
                                                        return Reply.builder().msg("Async operation is triggered. Webhook notification will be sent out after health check completed.").statusCode(200).build();
                                                }
                                                // Mark elapse time
                                                long hcEndTime = System.currentTimeMillis();
                                                log.debug("End timestamp: {}", hcEndTime);
                                                double hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                                                PdfContent pdfContent = PdfContent.builder().hostName(hostName)
                                                                .instanceName(instanceName)
                                                                .healthCheckContent(healthCheckContent)
                                                                .hcElapsedTime(hcElapsedTime).build();
                                                pdfContents.add(pdfContent);

                                        }

                                }
                        }

                        pdfService.generateDbHealthCheckPdf(fileName, requester,
                                        dateTime,
                                        pdfContents);
                        String base64 = this.pdfService.generateBase64FromPdf(fileName);

                        log.debug("filename {}", fileName);
                        log.debug("base64 {}", base64);

                        hcReturn = HealthCheckReturn.builder().statusCode(200)
                                        .file_output_name(fileName)
                                        .msg(String.format("Here is the %s health check report",
                                                        getType()))
                                        .pdfResult(base64)
                                        .build();
                        return hcReturn;
                } catch (SshException e) {
                        log.error("ssh exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report when SSH. Error: "
                                                        + e.getMessage())
                                        .build();
                } catch (Exception e) {
                        log.error("General exception", e);
                        return HealthCheckReturn.builder().statusCode(500)
                                        .msg("Failed to get the health check report. Error: "
                                                        + e.getMessage())
                                        .build();
                }
        }

        public static boolean isValidJson(String jsonString) {
                try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        objectMapper.readTree(jsonString);
                        Optional<HealthCheckKeyword> keyword = Optional
                                        .of(objectMapper.readValue(jsonString, HealthCheckKeyword.class));
                        return keyword.isPresent(); // Valid JSON
                } catch (Exception e) {
                        return false; // Invalid JSON
                }
        }
}
