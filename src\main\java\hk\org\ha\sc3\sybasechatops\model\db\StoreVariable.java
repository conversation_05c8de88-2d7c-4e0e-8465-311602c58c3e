package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_store_variable", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class StoreVariable {

    @Id
    private long id;

    private String name;

    private String regexpStr;

    @ManyToOne
    CommandGroupMapping cmdGrpMapping;

    @Transient
    private String value;

    private boolean mandatory = true;

}