package hk.org.ha.sc3.sybasechatops.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsChatroom;
import hk.org.ha.sc3.sybasechatops.repository.ChatroomRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * Service class for token management and validation
 * Handles token hashing, validation, and migration between plaintext and hashed tokens
 */
@Slf4j
@Service
public class TokenService {

    @Autowired
    private JasyptEncryptionService encryptionService;

    @Autowired
    private ChatroomRepository chatroomRepository;

    /**
     * Validates a token by checking against stored chatroom tokens
     * Supports both plaintext tokens (for backward compatibility) and hashed tokens
     *
     * @param plainToken The plaintext token to validate
     * @return true if the token is valid for any chatroom, false otherwise
     */
    public boolean validateToken(String plainToken) {
        if (plainToken == null || plainToken.isEmpty()) {
            log.warn("Token validation failed: null or empty token");
            return false;
        }

        try {
            // First, try to find chatroom with the plaintext token (backward compatibility)
            boolean existsAsPlaintext = chatroomRepository.existsByToken(plainToken);
            if (existsAsPlaintext) {
                log.debug("Token validated as plaintext token");
                return true;
            }

            // If not found as plaintext, hash the token and check for hashed version
            String hashedToken = encryptionService.hashToken(plainToken);
            boolean existsAsHash = chatroomRepository.existsByToken(hashedToken);
            if (existsAsHash) {
                log.debug("Token validated as hashed token");
                return true;
            }

            log.debug("Token validation failed: token not found in database");
            return false;
        } catch (Exception e) {
            log.error("Token validation failed due to exception", e);
            return false;
        }
    }

    /**
     * Validates a token for a specific room
     * Supports both plaintext and hashed tokens
     *
     * @param plainToken The plaintext token to validate
     * @param roomId The room ID to validate against
     * @return true if the token is valid for the specified room, false otherwise
     */
    public boolean validateTokenForRoom(String plainToken, String roomId) {
        if (plainToken == null || plainToken.isEmpty() || roomId == null || roomId.isEmpty()) {
            log.warn("Token validation failed: null or empty token or roomId");
            return false;
        }

        try {
            // Try plaintext token first (backward compatibility)
            boolean existsAsPlaintext = chatroomRepository.findByTokenAndRoomId(plainToken, roomId).isPresent();
            if (existsAsPlaintext) {
                log.debug("Token validated as plaintext for room: {}", roomId);
                return true;
            }

            // Try hashed token
            String hashedToken = encryptionService.hashToken(plainToken);
            boolean existsAsHash = chatroomRepository.findByTokenAndRoomId(hashedToken, roomId).isPresent();
            if (existsAsHash) {
                log.debug("Token validated as hash for room: {}", roomId);
                return true;
            }

            log.debug("Token validation failed for room: {}", roomId);
            return false;
        } catch (Exception e) {
            log.error("Token validation failed for room: {} due to exception", roomId, e);
            return false;
        }
    }
/**
     * Finds a chatroom by token and room ID, supporting both plaintext and hashed tokens
     * 
     * @param plainToken The plaintext token to search for
     * @param roomId The room ID to search for
     * @return Optional containing the ChatopsChatroom if found, empty otherwise
     */
    public java.util.Optional<ChatopsChatroom> findChatroomByTokenAndRoomId(String plainToken, String roomId) {
        if (plainToken == null || plainToken.isEmpty() || roomId == null || roomId.isEmpty()) {
            log.warn("Token or room ID is null or empty");
            return java.util.Optional.empty();
        }

        try {
            // First, try to find chatroom with the plaintext token (backward compatibility)
            java.util.Optional<ChatopsChatroom> chatroomOpt = chatroomRepository.findByTokenAndRoomId(plainToken, roomId);
            if (chatroomOpt.isPresent()) {
                log.debug("Found chatroom with plaintext token for room: {}", roomId);
                return chatroomOpt;
            }

            // If not found as plaintext, hash the token and check for hashed version
            String hashedToken = encryptionService.hashToken(plainToken);
            chatroomOpt = chatroomRepository.findByTokenAndRoomId(hashedToken, roomId);
            if (chatroomOpt.isPresent()) {
                log.debug("Found chatroom with hashed token for room: {}", roomId);
                return chatroomOpt;
            }

            log.debug("Chatroom not found for token and room: {}", roomId);
            return java.util.Optional.empty();
        } catch (Exception e) {
            log.error("Error finding chatroom by token and room ID: {}", roomId, e);
            return java.util.Optional.empty();
        }
    }

    /**
     * Hashes a plaintext token
     *
     * @param plainToken The plaintext token to hash
     * @return The SHA-256 hash of the token
     */
    public String hashToken(String plainToken) {
        return encryptionService.hashToken(plainToken);
    }

    /**
     * Migrates all chatroom tokens from plaintext to hashed
     *
     * @return The number of chatrooms successfully migrated
     */
    public long migrateAllChatroomTokensToHash() {
        long migratedCount = 0;
        try {
            java.util.List<ChatopsChatroom> allChatrooms = chatroomRepository.findAll();
            log.info("Starting migration of {} chatrooms", allChatrooms.size());

            for (ChatopsChatroom chatroom : allChatrooms) {
                String currentToken = chatroom.getToken();
                
                // Skip if already hashed
                if (encryptionService.isTokenHash(currentToken)) {
                    log.debug("Skipping already hashed token for roomId: {}", chatroom.getRoomId());
                    continue;
                }

                try {
                    String hashedToken = encryptionService.hashToken(currentToken);
                    chatroom.setToken(hashedToken);
                    chatroomRepository.save(chatroom);
                    migratedCount++;
                    log.debug("Migrated token for roomId: {}", chatroom.getRoomId());
                } catch (Exception e) {
                    log.error("Failed to migrate token for roomId: {}", chatroom.getRoomId(), e);
                }
            }

            log.info("Migration completed. Successfully migrated {} chatrooms", migratedCount);
        } catch (Exception e) {
            log.error("Migration process failed", e);
        }

        return migratedCount;
    }

    /**
     * Checks if a token is already in hashed format
     *
     * @param token The token to check
     * @return true if the token appears to be a hash, false otherwise
     */
    public boolean isTokenHashed(String token) {
        return encryptionService.isTokenHash(token);
    }
}