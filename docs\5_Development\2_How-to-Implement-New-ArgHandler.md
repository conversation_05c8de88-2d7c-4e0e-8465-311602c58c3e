# How to Implement a New ArgHandler

## Overview

ArgHandlers are Spring components responsible for processing command arguments in the SC3 ChatOps system. They provide dynamic options to users based on the current context and session state. This guide explains how to create a new ArgHandler from scratch.

## Architecture Overview

The ArgHandler system follows a plugin-like architecture where each handler is automatically discovered and registered by Spring's dependency injection container.

### Key Components

- **ICommandArgHandler**: Abstract base class that all ArgHandlers must extend
- **CmdArgEnum**: Enum defining all available argument types
- **Option**: Model representing a selectable option with text, value, and next label
- **CommandArgument**: Entity representing argument metadata from database
- **BaseMenuControllerV2**: Controller that orchestrates ArgHandler execution

## Package Structure

```
src/main/java/hk/org/ha/sc3/sybasechatops/component/arghandler/
├── common/          # General purpose handlers
│   ├── HostArgHandler.java
│   └── InstanceArgHandler.java
├── cdp/            # Cloudera-specific handlers
│   ├── CdpClusterArgHandler.java
│   └── CdpServiceArgHandler.java
└── your_category/  # Your new category
    └── YourArgHandler.java
```


### Architecture Diagram

The following diagram shows how ArgHandlers are integrated into the Spring application context:

```mermaid
graph TB
    subgraph "Spring Application Context"
        Controller[BaseMenuControllerV2]
        Handler1[HostArgHandler]
        Handler2[InstanceArgHandler]
        HandlerN[YourNewArgHandler]
    end

    subgraph "Core Interfaces"
        IHandler[ICommandArgHandler<br/>Abstract Class]
        CmdEnum[CmdArgEnum<br/>Enum]

        subgraph "Data Models"
            Option[Option<br/>Model]
            CommandArg[CommandArgument<br/>Entity]
            Session[HttpSession]
        end
    end

    subgraph "External Dependencies"
        DB[(Database)]
        Repo[Repository Layer]
    end

    Controller -->|Injects List of| Handler1
    Controller -->|Injects List of| Handler2
    Controller -->|Injects List of| HandlerN

    Handler1 -.->|extends| IHandler
    Handler2 -.->|extends| IHandler
    HandlerN -.->|extends| IHandler

    IHandler -->|returns| CmdEnum
    IHandler -->|creates| Option
    IHandler -->|receives| CommandArg
    IHandler -->|accesses| Session

    HandlerN -->|queries| Repo
    Repo -->|fetches from| DB

    style Controller fill:#e1f5fe
    style IHandler fill:#f3e5f5
    style HandlerN fill:#e8f5e8
    style Option fill:#fff3e0
```

## Execution Flow

The following sequence diagram illustrates how ArgHandlers are executed during command processing:

```mermaid
sequenceDiagram
    participant User
    participant Controller as BaseMenuControllerV2
    participant Handler as ArgHandler
    participant Session as HttpSession
    participant Repo as Repository
    participant DB as Database

    User->>Controller: Send command request
    Controller->>Controller: Parse msgChain
    Controller->>Controller: Determine required argument

    loop Find matching handler
        Controller->>Handler: getCmdArgType()
        Handler-->>Controller: Return CmdArgEnum
    end

    Controller->>Handler: getChatopsResp(argument, session, msgChain)
    Handler->>Handler: getOptions(argument, session, msgChain)

    Handler->>Session: getAttribute(previousArgs)
    Session-->>Handler: Return context values

    Handler->>Repo: Query data based on context
    Repo->>DB: Execute query
    DB-->>Repo: Return results
    Repo-->>Handler: Return entities

    loop For each entity
        Handler->>Handler: Create hiddenValue map
        Handler->>Handler: Serialize to JSON
        Handler->>Handler: Build Option object
    end

    Handler-->>Controller: Return List<Option>
    Controller->>Controller: Build AdancedMenu response
    Controller-->>User: Return menu with options

    User->>Controller: Select option
    Controller->>Session: Store selected value
    Controller->>Controller: Continue to next argument or execute command
```

## Step-by-Step Implementation Guide

### Implementation Flow

```mermaid
flowchart TD
    Start([Start Implementation]) --> Step1[Add to CmdArgEnum]
    Step1 --> Step2[Create ArgHandler Class]
    Step2 --> Step3[Extend ICommandArgHandler]
    Step3 --> Step4["Implement getCmdArgType()"]
    Step4 --> Step5["Implement getOptions()"]

    Step5 --> SubFlow1{Need Session Context?}
    SubFlow1 -->|Yes| Step6[Extract from HttpSession]
    SubFlow1 -->|No| Step7[Query Repository Directly]
    Step6 --> Step7

    Step7 --> Step8[Create Options List]
    Step8 --> Loop{For Each Data Item}
    Loop --> Step9[Create hiddenValue Map]
    Step9 --> Step10[Serialize to JSON]
    Step10 --> Step11[Build Option Object]
    Step11 --> Step12[Add to Options List]
    Step12 --> Loop

    Loop -->|Done| Step13[Return Options List]
    Step13 --> Step14[Write Unit Tests]
    Step14 --> Step15[Test Integration]
    Step15 --> Step16[Deploy & Verify]
    Step16 --> End([Complete])

    style Start fill:#e8f5e8
    style End fill:#ffebee
    style Step2 fill:#e3f2fd
    style Step5 fill:#fff3e0
    style Step14 fill:#f3e5f5
```

### Step 1: Add New Argument Type to CmdArgEnum

First, add your new argument type to the enum:

```java
// src/main/java/hk/org/ha/sc3/sybasechatops/constant/cmd/CmdArgEnum.java
public enum CmdArgEnum {
    // CDP arguments
    ARG_CDP_CLUSTER,
    ARG_CDP_SERVICE,
    ARG_CDP_HOST,
    ARG_CDP_INSTANCE,

    // Common arguments
    ARG_HOST,
    ARG_INSTANCE,
    
    // Your new argument type
    ARG_YOUR_NEW_TYPE
}
```

### Step 2: Create the ArgHandler Class

Create a new Java class that extends `ICommandArgHandler`:

```java
package hk.org.ha.sc3.sybasechatops.component.arghandler.your_category;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class YourNewArgHandler extends ICommandArgHandler {

    private YourDataRepository yourDataRepository;

    public YourNewArgHandler(YourDataRepository yourDataRepository) {
        this.yourDataRepository = yourDataRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_YOUR_NEW_TYPE;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        // Extract context from session if needed
        String contextValue = (String) httpSession.getAttribute("CONTEXT_KEY");

        // Fetch data from your repository
        List<YourDataEntity> dataList = yourDataRepository.findByContext(contextValue);

        for (YourDataEntity data : dataList) {
            hiddenValue.clear();
            hiddenValue.put(getCmdArgType().name(), data.getValue());
            
            try {
                String jsonStr = mapper.writeValueAsString(hiddenValue);
                Option option = Option.builder()
                    .text(data.getDisplayName()) // Text shown to user
                    .value(jsonStr) // JSON value stored in session
                    .nextLabel(msgChain + "|" + getCmdArgType())
                    .build();
                options.add(option);
            } catch (JsonProcessingException e) {
                log.error("Error processing JSON for data: {}", data.getValue(), e);
            }
        }

        return options;
    }
}
```

### Step 3: Key Implementation Details

#### 3.1 Package Structure
- Place common ArgHandlers in: `component.arghandler.common`
- Place CDP-specific ArgHandlers in: `component.arghandler.cdp`
- Create new categories as needed: `component.arghandler.your_category`

#### 3.2 Required Annotations
- `@Component`: Enables Spring auto-discovery
- `@Slf4j`: Provides logging capabilities

#### 3.3 Constructor Injection
- Inject required repositories or services via constructor
- Spring will automatically wire dependencies

#### 3.4 Option Creation Pattern
```java
// Standard pattern for creating options
hiddenValue.put(getCmdArgType().name(), actualValue);
String jsonStr = mapper.writeValueAsString(hiddenValue);
Option option = Option.builder()
    .text(displayText)           // What user sees
    .value(jsonStr)              // What gets stored
    .nextLabel(msgChain + "|" + getCmdArgType())  // Next step identifier
    .build();
```

### Step 4: Session Context Usage

ArgHandlers often depend on previously selected values stored in the session:

```java
// Retrieve previous selections
String previousArg = (String) httpSession.getAttribute(CmdArgEnum.ARG_PREVIOUS.name());
String team = (String) httpSession.getAttribute(SessionAttrEnum.TEAM.name());

// Use context to filter options
List<YourEntity> filteredData = repository.findByTeamAndPreviousArg(team, previousArg);
```

## Registration and Discovery

### Automatic Registration

ArgHandlers are automatically discovered and registered by Spring due to:

1. **@Component annotation**: Makes the class a Spring bean
2. **Constructor in BaseMenuControllerV2**: Accepts `List<ICommandArgHandler>`
3. **Spring's dependency injection**: Automatically injects all implementations

```java
// In BaseMenuControllerV2.java
public BaseMenuControllerV2(
    // ... other dependencies
    List<ICommandArgHandler> cmdArgHandlers) {
    
    this.cmdArgHandlers = cmdArgHandlers;
}
```

### Handler Lookup Process

The controller finds the appropriate handler using:

```java
for (ICommandArgHandler cmdArgHandler : this.cmdArgHandlers) {
    if (cmdArgHandler.getCmdArgType().equals(commandArgument.getArgType())) {
        response = cmdArgHandler.getChatopsResp(commandArgument, httpSession, msgChain);
        return response;
    }
}
```

## Testing Your ArgHandler

### Unit Test Template

```java
@ExtendWith(MockitoExtension.class)
class YourNewArgHandlerTest {

    @Mock
    private YourDataRepository yourDataRepository;

    @Mock
    private HttpSession httpSession;

    @InjectMocks
    private YourNewArgHandler argHandler;

    @Test
    void testGetCmdArgType() {
        assertEquals(CmdArgEnum.ARG_YOUR_NEW_TYPE, argHandler.getCmdArgType());
    }

    @Test
    void testGetOptions() {
        // Given
        CommandArgument argument = new CommandArgument();
        String msgChain = "test|chain";
        
        List<YourDataEntity> mockData = Arrays.asList(
            createMockEntity("value1", "Display 1"),
            createMockEntity("value2", "Display 2")
        );
        
        when(yourDataRepository.findAll()).thenReturn(mockData);

        // When
        List<Option> options = argHandler.getOptions(argument, httpSession, msgChain);

        // Then
        assertEquals(2, options.size());
        assertEquals("Display 1", options.get(0).getText());
        assertTrue(options.get(0).getValue().contains("value1"));
    }
}
```

## Database Configuration

### Preparing MyBatis SQL Deployment Package

Before your new ArgHandler can work, you need to create database entries for the command arguments and their mappings. This involves two main tables in the `health_check` schema:

#### 1. chatops_command_argument Table

**Field Descriptions:**
- `id`: Unique identifier for the argument (varchar, primary key)
- `key_name`: The argument name/key used internally
- `description`: User-friendly description shown in the UI
- `arg_type`: The CmdArgEnum value (must match your ArgHandler's getCmdArgType())

**Sample Entries from Migration:**
```sql
INSERT INTO health_check.chatops_command_argument (`id`, `key_name`, `description`, `arg_type`) VALUES
  ('1','cluster','Please select the cluster','CLOUDERA_CLUSTER'),
  ('2','service','Please select the service','CLOUDERA_SERVICE'),
  ('3','host','Please select the host','HOST');
```

#### 2. chatops_command_argument_mapping Table

This table creates a many-to-many relationship between commands and arguments, defining their sequence and properties.

**Field Descriptions:**
- `cmd_id`: References the command ID from chatops_command table
- `argument_id`: References the argument ID from chatops_command_argument table
- `sequence`: Order in which arguments are presented (int, starting from 1)
- `required`: Whether the argument is mandatory (1) or optional (0)
- `enabled`: Whether the argument is active (1) or disabled (0)

**Sample Entries from Migration:**
```sql
INSERT INTO health_check.chatops_command_argument_mapping (`cmd_id`, `argument_id`, `sequence`, `required`, `enabled`) VALUES
  ('CLOUDERA_API_CHK_ROLE', '1', 1, 1, 1),
  ('CLOUDERA_API_CHK_ROLE', '2', 2, 1, 1),
  ('ORAROLE', '3', 1, 1, 1);
```

#### Complete Example: Adding a New Database Schema Argument

#### MyBatis Migration File Structure

Follow the existing MyBatis migration pattern used in the project:

```
mybatis/db/migration/scripts/
└── YYYYMMDDNNN_TICKET-ID_DESCRIPTION.sql
```

**Example migration file (20250702001_ARGHANDLER-123_ADD_SCHEMA_ARGS.sql):**
```sql
-- Description: Add new database schema arguments for ArgHandler

-- Add new arguments
INSERT INTO health_check.chatops_command_argument (`id`, `key_name`, `description`, `arg_type`) VALUES
  ('schema_arg_001', 'database_schema', 'Please select the database schema', 'ARG_DB_SCHEMA'),
  ('table_arg_001', 'database_table', 'Please select the database table', 'ARG_DB_TABLE');

-- Map arguments to commands
INSERT INTO health_check.chatops_command_argument_mapping (`cmd_id`, `argument_id`, `sequence`, `required`, `enabled`) VALUES
  ('DB_SCHEMA_CHECK', 'schema_arg_001', 3, 1, 1),
  ('SCHEMA_ANALYSIS', 'schema_arg_001', 2, 1, 1),
  ('TABLE_ANALYSIS', 'schema_arg_001', 1, 1, 1),
  ('TABLE_ANALYSIS', 'table_arg_001', 2, 1, 1);

-- //@UNDO
-- Rollback mappings first (due to foreign key constraints)
DELETE FROM health_check.chatops_command_argument_mapping
WHERE argument_id IN ('schema_arg_001', 'table_arg_001');

-- Then rollback arguments
DELETE FROM health_check.chatops_command_argument
WHERE id IN ('schema_arg_001', 'table_arg_001');
```

## Example: Complete Implementation

For a complete working example, refer to the existing implementations:
- **HostArgHandler**: Simple host selection
- **InstanceArgHandler**: Database instance selection with host context
- **CdpClusterArgHandler**: Cloudera cluster selection

These examples demonstrate the patterns and best practices for integrating ArgHandlers with command group buttons in the SC3 ChatOps system.

## Troubleshooting

### Common Issues

1. **ArgHandler Not Found**
   - Verify the `@Component` annotation is present
   - Check that the package is scanned by Spring
   - Ensure `getCmdArgType()` returns the correct enum value
