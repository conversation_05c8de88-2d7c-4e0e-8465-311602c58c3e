package hk.org.ha.sc3.sybasechatops.model.db;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;


import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Type;

import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_command", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class Command {
    @Id
    private String id;
    private String user;
    private String command;
    private Integer timeout;

    @Enumerated(EnumType.STRING)
    private CmdTypeEnum cmdType;

    @Enumerated(EnumType.STRING)
    private CmdReturnEnum returnType;

    @Type(type = "org.hibernate.type.NumericBooleanType")
    private boolean enabled;

    @OneToMany(mappedBy = "command")
    private List<CommandGroupMapping> commandGroups;

    @Transient
    private HashMap<String, String> storeVariables;

    @OneToMany(mappedBy = "command", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("sequence ASC")
    private Set<CmdArgMapping> argumentMappings;

    @Transient
    public List<CommandArgument> getArguments() {
        if (argumentMappings == null) {
            return Collections.emptyList();
        }
        return argumentMappings.stream()
                               .map(CmdArgMapping::getCommandArgument)
                               .collect(Collectors.toList());
    }

    public String getHost(){
        return storeVariables.get(CmdArgEnum.ARG_HOST.name());
    }

    public String getInstance(){
        return storeVariables.get(CmdArgEnum.ARG_INSTANCE.name());
    }

    public String getCommand() {
        String cmdReplaced = this.command;
        
        String instance = this.getInstance();
        String host = this.getHost();
        
        if (instance != null) {
            cmdReplaced = cmdReplaced.replace("%instance%", instance);
        }
        
        if (host != null) {
            cmdReplaced = cmdReplaced.replace("%host%", host);
        }
        
        for (Entry<String, String> entry : this.storeVariables.entrySet()) {
            if (entry.getValue() != null) {
                cmdReplaced = cmdReplaced.replace("%" + entry.getKey() + "%", entry.getValue());
            }
        }
        return cmdReplaced;
    }

}
