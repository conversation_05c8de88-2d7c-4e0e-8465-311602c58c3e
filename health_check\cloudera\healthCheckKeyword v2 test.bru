meta {
  name: healthCheck<PERSON>eyword v2 test
  type: http
  seq: 1
}

post {
  url: http://localhost:8080/chatops/menu/cloudera/health_check
  body: json
  auth: none
}

body:json {
  {
    "requester": "Bon LAI",
    "requestTime": "**********",
    "msg<PERSON>hain": "@itbot|Cloudera|sc3_sand01|HIVE_ON_TEZ|CLOUDERA_API_CHK_ROLE",
    "corpID": "lkp625",
    "keyword": "{\"items\":[{\"commandGroupId\":\"CLOUDERA_API_CHK_ROLE\",\"storeVariablesMap\":{\"service\":\"HIVE_ON_TEZ\",\"clusterName\":\"sc3_sand01\"}}]}",
    "chatopsEnv": "{{env}}",
    "roomID": "{{room_id}}",
    "token": "{{chatops_token}}"
  }
}
