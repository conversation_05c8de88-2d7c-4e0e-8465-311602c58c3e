package hk.org.ha.sc3.sybasechatops.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "webdriver")
public class WebDriverConfig {
    private int windowHeight = 1080;
    private int windowWidth = 1920;
    private String location;
    private boolean isHeadless;
    private String binaryPath;
}
