package hk.org.ha.sc3.sybasechatops.helper;

import java.awt.Color;
import java.util.HashMap;
import java.util.Map;

public class AnsiColorConverter {

    private static final Map<String, Color> ANSI_TO_COLOR_MAP = new HashMap<>();

    static {
        ANSI_TO_COLOR_MAP.put("0;30", Color.BLACK);
        ANSI_TO_COLOR_MAP.put("0;31", Color.RED);
        ANSI_TO_COLOR_MAP.put("0;32", Color.GREEN);
        ANSI_TO_COLOR_MAP.put("0;33", Color.YELLOW);
        ANSI_TO_COLOR_MAP.put("0;34", Color.BLUE);
        ANSI_TO_COLOR_MAP.put("0;35", new Color(255, 0, 255)); // Magenta
        ANSI_TO_COLOR_MAP.put("0;36", Color.CYAN);
        ANSI_TO_COLOR_MAP.put("0;37", Color.WHITE);
        ANSI_TO_COLOR_MAP.put("1;30", new Color(128, 128, 128)); // Gray
        ANSI_TO_COLOR_MAP.put("1;31", new Color(255, 0, 0)); // Bright red
        ANSI_TO_COLOR_MAP.put("1;32", new Color(0, 255, 0)); // Bright green
        ANSI_TO_COLOR_MAP.put("1;33", new Color(255, 255, 0)); // Bright yellow
        ANSI_TO_COLOR_MAP.put("1;34", new Color(0, 0, 255)); // Bright blue
        ANSI_TO_COLOR_MAP.put("1;35", new Color(255, 0, 255)); // Bright magenta
        ANSI_TO_COLOR_MAP.put("1;36", new Color(0, 255, 255)); // Bright cyan
        ANSI_TO_COLOR_MAP.put("1;37", Color.WHITE);
    }

    public static Color convertAnsiColor(String ansiColorCode) {
        return ANSI_TO_COLOR_MAP.getOrDefault(ansiColorCode, Color.WHITE);
    }
}