package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.checkerframework.common.aliasing.qual.Unique;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_ansible_inventory", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class AnsibleInventory {
    @Id
    private Long id;

    @Unique
    private String hostname;
}
