# Spring Boot Test Coverage Guide

## 🎯 **Best Practice: JaCoCo + Maven**

This is the **industry standard** for Spring Boot test coverage. Simple, reliable, and well-integrated.

## ⚡ **Quick Commands**

```bash
# Run tests with coverage
mvn clean test

# Generate coverage reports
mvn jacoco:report

# View interactive HTML report
start target/site/jacoco/index.html
```

## 📊 **Generated Reports**

After running the commands above, you'll find:

- **`target/site/jacoco/index.html`** - 📱 Interactive HTML report (best for developers)
- **`target/site/jacoco/jacoco.xml`** - 🔧 XML format (for CI/CD tools like SonarQube)
- **`target/site/jacoco/jacoco.csv`** - 📈 CSV format (for data analysis)

## 🏗️ **What's Configured**

Your `pom.xml` includes:

1. **JaCoCo Maven Plugin** - Generates coverage during tests
2. **Quality Gates** - Enforces 60% minimum coverage
3. **Multiple Formats** - HTML, XML, CSV reports
4. **CI/CD Ready** - Works with SonarQube, Codecov, etc.

## 📈 **Coverage Metrics**

- **Line Coverage** - % of code lines executed
- **Branch Coverage** - % of decision branches taken  
- **Method Coverage** - % of methods called
- **Class Coverage** - % of classes instantiated

## 🎯 **Recommended Coverage Goals**

- **Unit Tests**: 80-90% line coverage
- **Integration Tests**: Focus on critical business paths
- **Overall Project**: 60-70% (enforced by quality gate)

## 🚀 **CI/CD Integration Examples**

### GitHub Actions
```yaml
- name: Run Tests with Coverage
  run: mvn clean verify

- name: Upload to Codecov
  uses: codecov/codecov-action@v3
  with:
    file: target/site/jacoco/jacoco.xml
```

### SonarQube
```bash
mvn clean verify sonar:sonar \
  -Dsonar.projectKey=chatops-spring \
  -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml
```

## 🔧 **Common Commands**

```bash
# Basic coverage generation
mvn test jacoco:report

# With clean build
mvn clean test jacoco:report

# Check coverage thresholds
mvn jacoco:check

# Run specific test class
mvn test -Dtest=TokenServiceTest

# Skip tests but generate report from existing data
mvn jacoco:report -DskipTests
```

## 🎉 **Why This Approach is Best**

- ✅ **Industry Standard** - Used by most Spring Boot projects
- ✅ **Zero Configuration** - Works out of the box
- ✅ **IDE Integration** - IntelliJ/Eclipse support
- ✅ **CI/CD Ready** - Integrates with all major platforms
- ✅ **Multiple Formats** - HTML for developers, XML for tools
- ✅ **Quality Gates** - Enforces coverage standards
- ✅ **No External Dependencies** - Pure Maven/Java solution

## 📋 **Your Current Test Coverage**

Your project has good integration test coverage for:
- ✅ **Services** (Ansible, Webhook, SSH, etc.)
- ✅ **Database Layer** (JPA repositories)
- ✅ **External Integrations** (Cyberark, Grafana)
- ✅ **PDF Generation**
- ✅ **Encryption Services**

### Next Steps to Improve Coverage

1. **Add Unit Tests** - Test business logic in isolation with mocks
2. **Add Controller Tests** - Use `@WebMvcTest` for web layer
3. **Test Error Scenarios** - Cover exception handling
4. **Mock External Dependencies** - Reduce test execution time

For detailed documentation, see: [docs/spring-boot-test-coverage.md](docs/spring-boot-test-coverage.md)
