package hk.org.ha.sc3.sybasechatops.service;

import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import lombok.Setter;

@Setter
public class CommandContext {
    private ICommand commandExecutor;

    public CommandResult executeCommand(Command command) throws Exception {
        CommandResult result=commandExecutor.execByCommand(command);
        result.setReturnType(command.getReturnType());
        return result;
    }
}
