package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "function", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class Function {

    private String name;

    @Id
    private String abbr;

    @OneToMany(mappedBy = "id.function", fetch = FetchType.LAZY)
    private List<DatabaseFunction> dbFunctions;

}
