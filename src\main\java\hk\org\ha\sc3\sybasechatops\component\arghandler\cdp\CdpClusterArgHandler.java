package hk.org.ha.sc3.sybasechatops.component.arghandler.cdp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaCluster;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaClusterRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CdpClusterArgHandler extends ICommandArgHandler {

    private ClouderaClusterRepository clouderaClusterRepository;

    public CdpClusterArgHandler(ClouderaClusterRepository clouderaClusterRepository) {
        this.clouderaClusterRepository = clouderaClusterRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_CDP_CLUSTER;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        for (ClouderaCluster cluster : clouderaClusterRepository.findAll()) {
            hiddenValue.put(getCmdArgType().name(), cluster.getClusterName());
            try {
                hiddenValue.put(getCmdArgType().name(), cluster.getClusterName());
                String jsonStr = mapper.writeValueAsString(hiddenValue);
                Option option = Option.builder().text(cluster.getClusterName()) // Text displayed to the user
                        .value(jsonStr) // Value to be appended to msgChain when selected
                        .nextLabel(msgChain + "|" + getCmdArgType()).build();
                options.add(option);
            } catch (JsonProcessingException e) {
                log.error("Error processing JSON for cluster: {}", cluster.getClusterName(), e);
            }
        }

        return options;
    }
}
