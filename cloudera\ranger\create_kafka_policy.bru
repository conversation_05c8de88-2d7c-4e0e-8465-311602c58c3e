meta {
  name: create_kafka_policy
  type: http
  seq: 1
}

post {
  url: https://bdvmc2a.server.ha.org.hk:6182/service/public/v2/api/policy
  body: json
  auth: basic
}

auth:basic {
  username: admin
  password: 
}

body:json {
    {
        "isEnabled": true,
        "version": 2,
        "service": "cm_kafka",
        "name": "Policy - BIS4 Topic bdi_otrs_tx_json test",
        "policyType": 0,
        "policyPriority": 0,
        "description": "RID20250203-151384",
        "isAuditEnabled": true,
        "resources": {
          "topic": {
            "values": [
              "bdi_otrs_tx_json"
            ],
            "isExcludes": false,
            "isRecursive": false
          }
        },
        "policyItems": [
          {
            "accesses": [
              {
                "type": "consume",
                "isAllowed": true
              },
              {
                "type": "describe",
                "isAllowed": true
              }
            ],
            "users": [
              "bdiadm",
              "bdisvc",
              "bdisup",
              "bdi01sup",
              "hive"
            ],
            "groups": [],
            "roles": [],
            "conditions": [
              {
                "type": "ip-range",
                "values": [
                  "*"
                ]
              }
            ],
            "delegateAdmin": false
          },
          {
            "accesses": [
              {
                "type": "publish",
                "isAllowed": true
              },
              {
                "type": "describe",
                "isAllowed": true
              }
            ],
            "users": [
              "bdiadm",
              "bdisvc",
              "dzeai"
            ],
            "groups": [],
            "roles": [],
            "conditions": [
              {
                "type": "ip-range",
                "values": [
                  "*"
                ]
              }
            ],
            "delegateAdmin": false
          }
        ],
        "denyPolicyItems": [],
        "allowExceptions": [],
        "denyExceptions": [],
        "dataMaskPolicyItems": [],
        "rowFilterPolicyItems": [],
        "serviceType": "kafka",
        "options": {},
        "validitySchedules": [],
        "policyLabels": [],
        "zoneName": "",
        "isDenyAllElse": false
      }
}
