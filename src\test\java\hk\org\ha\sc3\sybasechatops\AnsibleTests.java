package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.io.IOException;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.model.AnsibleListReturn;

import hk.org.ha.sc3.sybasechatops.service.AnsibleService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
public class AnsibleTests {

	@Autowired
	private AnsibleService ansibleService;

	@Test
	void testLaunchJobTemplateByName() throws IOException {
		AnsibleListReturn ansibleListReturn = ansibleService.getJobIdByJobTemplateMono("odc-ora-cims-healthcheck")
				.block();
		assertEquals(1035, ansibleListReturn.getResults().get(0).getId());
	}
}
