meta {
  name: orarole healthCheck v2
  type: http
  seq: 1
}

post {
  url: http://localhost:8080/chatops/v2/health_check
  body: json
  auth: none
}

body:json {
  {
    "requester": "Bon LAI",
    "requestTime": "**********",
    "msgChain": "@itbot|SC3 Database Health Check|46a66ddf-c1ed-4261-b232-381669d4928e|CLOUDERA|CLOUDERA_API_CHK_ROLE|ARG_CDP_CLUSTER|ARG_CDP_SERVICE",
    "corpID": "lkp625",
    "keyword": "{{session_id}}",
    "chatopsEnv": "{{env}}",
    "roomID": "{{room_id}}",
    "token": "{{chatops_token}}"
  }
}
