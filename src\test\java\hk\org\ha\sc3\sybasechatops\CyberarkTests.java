package hk.org.ha.sc3.sybasechatops;

import java.io.IOException;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.model.cyberark.AccountsResponse;
import hk.org.ha.sc3.sybasechatops.service.CyberarkService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class CyberarkTests {

	@Autowired
	private CyberarkService cyberarkService;

	@Test
	void testGetToken() throws IOException {
		AccountsResponse resp = cyberarkService.getAccount("tossadm CMS_CORP3_SP1_SB preapproval").block();
		log.info("count: {}", resp.getCount());
		log.info("id: {}", resp.getValue().get(0).getId());
	}

	@Test
	void testGetPassword() throws IOException {
		log.info(cyberarkService.getPasswordFromId("43_345").block());
	}

	@Test
	void testGetAccountListLargerOne() throws IOException {
		cyberarkService.getAccount("tossadm cdcibm41 preapproval").doOnNext(accountsResponse -> {
			if (accountsResponse.getCount() > 1) {
				throw new RuntimeException(
						"Cyberark account list count > 1. Please check the filtering parameter 'tossadm cdcibm41 preapproval'.");
			}
		}).flatMap(accountsResponse -> cyberarkService.getPasswordFromId(accountsResponse.getValue().get(0).getId()))
				.block();

	}

	@Test
	void testGetAccountAndPassword() throws IOException {
		log.info(cyberarkService.getAccountPassword("tossadm", "DC6COSTDB01").block());

	}

}
