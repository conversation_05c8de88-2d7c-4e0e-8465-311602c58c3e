package hk.org.ha.sc3.sybasechatops.filter;

import java.io.IOException;
import java.util.stream.Collectors;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.service.TokenService;

@Component
public class TokenAuthorizationFilter extends OncePerRequestFilter {

    private TokenService tokenService;

    public TokenAuthorizationFilter(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Bypass token checking for healthcheckchatops URL
        if (request.getRequestURI().contains("healthcheckchatops")) {
            filterChain.doFilter(request, response);
            return;
        }

        CachedBodyHttpServletRequest cachedBodyHttpServletRequest = new CachedBodyHttpServletRequest(request);
        String token = getTokenFromRequestBody(cachedBodyHttpServletRequest);

        if (token != null && validateToken(token)) {
            filterChain.doFilter(cachedBodyHttpServletRequest, response);
        } else {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Invalid token");
        }
    }

    private String getTokenFromRequestBody(HttpServletRequest request) throws IOException {
        String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));

        // Extract token from JSON body
        // For example, if the token is in a "token" field:
        JsonNode json = new ObjectMapper().readTree(requestBody);
        return json.get("token").asText();
    }

    private boolean validateToken(String token) {
        // Use TokenService for enhanced token validation with hash support
        return this.tokenService.validateToken(token);
    }

}