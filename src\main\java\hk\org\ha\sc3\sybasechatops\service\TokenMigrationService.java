package hk.org.ha.sc3.sybasechatops.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsChatroom;
import hk.org.ha.sc3.sybasechatops.repository.ChatroomRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for migrating chatroom tokens from plaintext to hashed format
 * Can be enabled via application property: chatops.security.token.enable-migration=true
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "chatops.security.token.enable-migration", havingValue = "true")
public class TokenMigrationService implements CommandLineRunner {

    @Autowired
    private JasyptEncryptionService encryptionService;

    @Autowired
    private ChatroomRepository chatroomRepository;

    /**
     * Automatically run migration on application startup if enabled
     */
    @Override
    public void run(String... args) throws Exception {
        log.info("Token migration service enabled - starting migration process");
        
        try {
            MigrationResult result = performMigration();
            log.info("Migration completed successfully. Migrated: {}, Skipped: {}, Failed: {}", 
                result.migrated, result.skipped, result.failed);
        } catch (Exception e) {
            log.error("Migration failed", e);
        }
    }

    /**
     * Performs the migration of all chatroom tokens from plaintext to hash
     *
     * @return MigrationResult containing statistics about the migration
     */
    @Transactional
    public MigrationResult performMigration() {
        log.info("Starting token migration process");
        
        MigrationResult result = new MigrationResult();
        
        try {
            java.util.List<ChatopsChatroom> allChatrooms = chatroomRepository.findAll();
            log.info("Found {} chatrooms to process", allChatrooms.size());
            
            for (ChatopsChatroom chatroom : allChatrooms) {
                try {
                    MigrationStatus status = migrateSingleChatroom(chatroom);
                    switch (status) {
                        case MIGRATED:
                            result.migrated++;
                            break;
                        case SKIPPED:
                            result.skipped++;
                            break;
                        case FAILED:
                            result.failed++;
                            break;
                    }
                } catch (Exception e) {
                    log.error("Failed to migrate chatroom: {}", chatroom.getRoomId(), e);
                    result.failed++;
                }
            }
            
            log.info("Migration process completed. Total processed: {}, Migrated: {}, Skipped: {}, Failed: {}", 
                allChatrooms.size(), result.migrated, result.skipped, result.failed);
                
        } catch (Exception e) {
            log.error("Migration process failed", e);
            throw e;
        }
        
        return result;
    }

    /**
     * Migrates a single chatroom's token if needed
     *
     * @param chatroom The chatroom to migrate
     * @return MigrationStatus indicating the result
     */
    private MigrationStatus migrateSingleChatroom(ChatopsChatroom chatroom) {
        String roomId = chatroom.getRoomId();
        String currentToken = chatroom.getToken();
        
        if (currentToken == null || currentToken.isEmpty()) {
            log.warn("Skipping chatroom {} - null or empty token", roomId);
            return MigrationStatus.SKIPPED;
        }
        
        // Check if token is already hashed
        if (encryptionService.isTokenHash(currentToken)) {
            log.debug("Skipping chatroom {} - token already hashed", roomId);
            return MigrationStatus.SKIPPED;
        }
        
        try {
            // Hash the plaintext token
            String hashedToken = encryptionService.hashToken(currentToken);
            
            // Create a backup record for rollback if needed
            log.debug("Migrating chatroom {} from plaintext to hash", roomId);
            
            // Update the token
            chatroom.setToken(hashedToken);
            chatroomRepository.save(chatroom);
            
            log.info("Successfully migrated token for chatroom: {}", roomId);
            return MigrationStatus.MIGRATED;
            
        } catch (Exception e) {
            log.error("Failed to migrate token for chatroom: {}", roomId, e);
            return MigrationStatus.FAILED;
        }
    }

    /**
     * Creates a backup of all chatroom tokens before migration
     * This can be used for rollback if needed
     *
     * @return The number of backup records created
     */
    public int createTokenBackup() {
        log.info("Creating token backup before migration");
        
        try {
            java.util.List<ChatopsChatroom> allChatrooms = chatroomRepository.findAll();
            int backupCount = 0;
            
            for (ChatopsChatroom chatroom : allChatrooms) {
                // In a real implementation, you might save to a backup table
                // For now, we'll just log the original tokens
                if (!encryptionService.isTokenHash(chatroom.getToken())) {
                    log.debug("Backup token for room {}: {}", chatroom.getRoomId(), 
                        maskToken(chatroom.getToken()));
                    backupCount++;
                }
            }
            
            log.info("Created backup for {} chatroom tokens", backupCount);
            return backupCount;
            
        } catch (Exception e) {
            log.error("Failed to create token backup", e);
            throw new RuntimeException("Backup creation failed", e);
        }
    }

    /**
     * Validates that migration was successful by checking token formats
     *
     * @return ValidationResult containing validation statistics
     */
    public ValidationResult validateMigration() {
        log.info("Validating migration results");
        
        ValidationResult result = new ValidationResult();
        
        try {
            java.util.List<ChatopsChatroom> allChatrooms = chatroomRepository.findAll();
            
            for (ChatopsChatroom chatroom : allChatrooms) {
                String token = chatroom.getToken();
                
                if (token == null || token.isEmpty()) {
                    result.nullTokens++;
                    log.warn("Found chatroom with null/empty token: {}", chatroom.getRoomId());
                } else if (encryptionService.isTokenHash(token)) {
                    result.hashedTokens++;
                } else {
                    result.plaintextTokens++;
                    log.warn("Found chatroom with plaintext token: {}", chatroom.getRoomId());
                }
                
                result.totalChatrooms++;
            }
            
            log.info("Migration validation completed. Total: {}, Hashed: {}, Plaintext: {}, Null: {}", 
                result.totalChatrooms, result.hashedTokens, result.plaintextTokens, result.nullTokens);
                
        } catch (Exception e) {
            log.error("Migration validation failed", e);
            throw e;
        }
        
        return result;
    }

    /**
     * Masks a token for logging purposes (shows only first and last 4 characters)
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "****";
        }
        return token.substring(0, 4) + "****" + token.substring(token.length() - 4);
    }

    /**
     * Result of migration operation
     */
    public static class MigrationResult {
        public int migrated = 0;
        public int skipped = 0;
        public int failed = 0;
        
        public int getTotal() {
            return migrated + skipped + failed;
        }
        
        public boolean isSuccessful() {
            return failed == 0;
        }
        
        @Override
        public String toString() {
            return String.format("MigrationResult{total=%d, migrated=%d, skipped=%d, failed=%d}", 
                getTotal(), migrated, skipped, failed);
        }
    }

    /**
     * Result of migration validation
     */
    public static class ValidationResult {
        public int totalChatrooms = 0;
        public int hashedTokens = 0;
        public int plaintextTokens = 0;
        public int nullTokens = 0;
        
        public boolean isValid() {
            return plaintextTokens == 0 && nullTokens == 0;
        }
        
        @Override
        public String toString() {
            return String.format("ValidationResult{total=%d, hashed=%d, plaintext=%d, null=%d}", 
                totalChatrooms, hashedTokens, plaintextTokens, nullTokens);
        }
    }

    /**
     * Status of individual chatroom migration
     */
    private enum MigrationStatus {
        MIGRATED,   // Successfully migrated from plaintext to hash
        SKIPPED,    // Skipped because already hashed or invalid
        FAILED      // Migration failed due to error
    }
}