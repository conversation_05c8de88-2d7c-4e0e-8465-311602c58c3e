---
sidebar_position: 3
---

# How to Implement a New Command Service

## Overview

Command services in the SC3 ChatOps system are responsible for executing different types of commands (SSH, Grafana, Cloudera API, Ansible, etc.). This guide provides a comprehensive walkthrough for implementing a new command service from scratch, including the necessary database configurations, ArgHandler integration, and testing procedures.

## Architecture Overview

The command service architecture follows a strategy pattern where different command types are handled by specific service implementations:

### Key Components

- **ICommand Interface**: Contract that all command services must implement
- **CmdTypeEnum**: Enum defining available command execution types
- **CommandService**: Abstract service that routes commands to appropriate executors
- **CommandContext**: Context object that manages command execution
- **Command Entity**: Database entity representing command metadata
- **CommandResult**: Result object containing execution output

### Command Execution Flow

```mermaid
sequenceDiagram
    participant User
    participant Controller as BaseMenuControllerV2
    participant CmdGroupService as CommandGroupService
    participant CmdContext as CommandContext
    participant CmdService as YourCommandService
    participant External as External System

    User->>Controller: Execute command group
    Controller->>CmdGroupService: execByCommandGroupId()

    loop For each command in group
        CmdGroupService->>CmdGroupService: Find matching ICommand service
        CmdGroupService->>CmdContext: setCommandExecutor()
        CmdGroupService->>CmdContext: executeCommand()
        CmdContext->>CmdService: execByCommand()
        CmdService->>External: Execute command
        External-->>CmdService: Return result
        CmdService-->>CmdContext: CommandResult
        CmdContext-->>CmdGroupService: CommandResult with return type
        CmdGroupService->>CmdGroupService: Process store variables
    end

    CmdGroupService-->>Controller: List<CommandResult>
    Controller-->>User: Formatted response
```

## Step-by-Step Implementation Guide

### Implementation Roadmap

```mermaid
flowchart TD
    Start([Start Implementation]) --> Step1[Add to CmdTypeEnum]
    Step1 --> Step2[Create Command Service Class]
    Step2 --> Step3[Implement ICommand Interface]
    Step3 --> Step4[Add Service Registration]
    Step4 --> Step5[Create Database Entries]
    Step5 --> Step6[Implement ArgHandlers]
    Step6 --> Step7[Write Unit Tests]
    Step7 --> Step8[Integration Testing]
    Step8 --> Step9[Deploy & Verify]
    Step9 --> End([Complete])

    style Start fill:#e8f5e8
    style End fill:#ffebee
    style Step2 fill:#e3f2fd
    style Step5 fill:#fff3e0
    style Step7 fill:#f3e5f5
```

### Step 1: Add New Command Type to CmdTypeEnum

First, extend the command type enumeration to include your new service type:

<augment_code_snippet path="src/main/java/hk/org/ha/sc3/sybasechatops/constant/CmdTypeEnum.java" mode="EXCERPT">
````java
public enum CmdTypeEnum {
    SSH,
    GRAFANA,
    CLOUDERA_API,
    ANSIBLE,
    YOUR_NEW_TYPE  // Add your new command type here
}
````
</augment_code_snippet>

### Step 2: Create the Command Service Class

Create a new service class that implements the `ICommand` interface:

```java
package hk.org.ha.sc3.sybasechatops.service;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;

@Slf4j
@Service
public class YourNewCommandService implements ICommand {

    private YourExternalApiClient apiClient;
    private YourConfigurationService configService;

    public YourNewCommandService(YourExternalApiClient apiClient,
                                YourConfigurationService configService) {
        this.apiClient = apiClient;
        this.configService = configService;
    }

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        log.info("Executing {} command: {}", getCmdType(), command.getCommand());

        try {
            // Extract parameters from command
            String host = command.getHost();
            String instance = command.getInstance();
            String commandStr = command.getCommand();

            // Execute the command based on command string
            String result = executeSpecificCommand(commandStr, host, instance);

            return CommandResult.builder()
                .stdout(result)
                .stderr("")
                .rc(0)
                .command(commandStr)
                .build();

        } catch (Exception e) {
            log.error("Error executing {} command: {}", getCmdType(), command.getCommand(), e);
            return CommandResult.builder()
                .stdout("")
                .stderr("Error: " + e.getMessage())
                .rc(-1)
                .command(command.getCommand())
                .build();
        }
    }

    @Override
    public CmdTypeEnum getCmdType() throws Exception {
        return CmdTypeEnum.YOUR_NEW_TYPE;
    }

    private String executeSpecificCommand(String commandStr, String host, String instance)
            throws Exception {
        // Implement your specific command execution logic here
        switch (commandStr) {
            case "getStatus":
                return getSystemStatus(host, instance);
            case "getMetrics":
                return getSystemMetrics(host, instance);
            case "restart":
                return restartService(host, instance);
            default:
                throw new IllegalArgumentException("Unknown command: " + commandStr);
        }
    }

    private String getSystemStatus(String host, String instance) throws Exception {
        // Implement status check logic
        return apiClient.getStatus(host, instance);
    }

    private String getSystemMetrics(String host, String instance) throws Exception {
        // Implement metrics retrieval logic
        return apiClient.getMetrics(host, instance);
    }

    private String restartService(String host, String instance) throws Exception {
        // Implement service restart logic
        return apiClient.restartService(host, instance);
    }
}
```

### Step 3: Key Implementation Details

#### 3.1 Required Annotations
- `@Service`: Enables Spring auto-discovery and dependency injection
- `@Slf4j`: Provides logging capabilities via Lombok

#### 3.2 Constructor Injection
- Inject required external clients, configuration services, or repositories
- Spring will automatically wire dependencies based on type

#### 3.3 Error Handling Pattern
```java
try {
    // Command execution logic
    String result = executeCommand();
    return CommandResult.builder()
        .stdout(result)
        .stderr("")
        .rc(0)  // Success
        .build();
} catch (Exception e) {
    log.error("Command execution failed", e);
    return CommandResult.builder()
        .stdout("")
        .stderr("Error: " + e.getMessage())
        .rc(-1)  // Failure
        .build();
}
```

#### 3.4 Command Parameter Access
Commands can access arguments through the Command object:

```java
// Access host and instance from ArgHandler selections
String host = command.getHost();           // From ARG_HOST
String instance = command.getInstance();   // From ARG_INSTANCE

// Access custom arguments from store variables
String customArg = command.getStoreVariables().get("ARG_CUSTOM_TYPE");
```

### Step 4: Service Registration and Discovery

#### 4.1 Automatic Registration

Command services are automatically discovered by Spring and injected into `CommandGroupService`:

<augment_code_snippet path="src/main/java/hk/org/ha/sc3/sybasechatops/service/CommandGroupService.java" mode="EXCERPT">
````java
for(ICommand command:cmdServices){
    if(cmd.getCmdType() == command.getCmdType()){
        cmdContext.setCommandExecutor(command);
    }
}
````
</augment_code_snippet>

#### 4.2 Service Lookup Process

The system finds the appropriate service using the command's `cmdType` field:

1. **Command Group Execution**: `CommandGroupService.execGroup()` iterates through commands
2. **Service Matching**: Finds `ICommand` implementation where `getCmdType()` matches `cmd.getCmdType()`
3. **Context Setting**: Sets the matched service as the command executor
4. **Execution**: Calls `execByCommand()` on the matched service

## Database Configuration

### Required Database Tables

Your new command service requires entries in several database tables to function properly:

#### 1. chatops_command Table

This table stores individual command definitions:

```sql
-- Example command entries for your new service
INSERT INTO health_check.chatops_command
(`id`, `user`, `command`, `timeout`, `cmd_type`, `return_type`, `enabled`) VALUES
('YOUR_SERVICE_STATUS', 'admin', 'getStatus', 30, 'YOUR_NEW_TYPE', 'TEXT', 1),
('YOUR_SERVICE_METRICS', 'admin', 'getMetrics', 60, 'YOUR_NEW_TYPE', 'TEXT', 1),
('YOUR_SERVICE_RESTART', 'admin', 'restart', 120, 'YOUR_NEW_TYPE', 'TEXT', 1);
```

**Field Descriptions:**
- `id`: Unique command identifier
- `user`: System user for command execution
- `command`: Command string (maps to your service's switch statement)
- `timeout`: Maximum execution time in seconds
- `cmd_type`: Must match your CmdTypeEnum value
- `return_type`: Output format (TEXT, JSON, PDF, etc.)
- `enabled`: Whether command is active (1) or disabled (0)

#### 2. chatops_command_group Table

Groups related commands together:

```sql
INSERT INTO health_check.chatops_command_group
(`id`, `db_type`, `cmd_type`, `enabled`, `is_approval`) VALUES
('YOUR_SERVICE_MONITORING', 'GENERIC', 'INSTANCE', 1, 0),
('YOUR_SERVICE_MANAGEMENT', 'GENERIC', 'HOST', 1, 1);
```

#### 3. chatops_command_group_mapping Table

Maps commands to command groups with execution order:

```sql
INSERT INTO health_check.chatops_command_group_mapping
(`cmd_id`, `command_group_id`, `sequence`, `exec_mode`, `pdf_title`) VALUES
('YOUR_SERVICE_STATUS', 'YOUR_SERVICE_MONITORING', 1, 'SEQUENTIAL', 'Service Status Report'),
('YOUR_SERVICE_METRICS', 'YOUR_SERVICE_MONITORING', 2, 'SEQUENTIAL', 'Service Metrics Report'),
('YOUR_SERVICE_RESTART', 'YOUR_SERVICE_MANAGEMENT', 1, 'SEQUENTIAL', 'Service Restart Log');
```

### MyBatis Migration File

Create a migration file following the project's naming convention:

```
mybatis/db/migration/scripts/YYYYMMDDNNN_TICKET-ID_DESCRIPTION.sql
```

**Example migration (20250702002_CMDSERVICE-456_ADD_NEW_SERVICE.sql):**

```sql
-- Description: Add new command service entries

-- Add commands
INSERT INTO health_check.chatops_command
(`id`, `user`, `command`, `timeout`, `cmd_type`, `return_type`, `enabled`) VALUES
('YOUR_SERVICE_STATUS', 'admin', 'getStatus', 30, 'YOUR_NEW_TYPE', 'TEXT', 1),
('YOUR_SERVICE_METRICS', 'admin', 'getMetrics', 60, 'YOUR_NEW_TYPE', 'TEXT', 1),
('YOUR_SERVICE_RESTART', 'admin', 'restart', 120, 'YOUR_NEW_TYPE', 'TEXT', 1);

-- Add command groups
INSERT INTO health_check.chatops_command_group
(`id`, `db_type`, `cmd_type`, `enabled`, `is_approval`) VALUES
('YOUR_SERVICE_MONITORING', 'GENERIC', 'INSTANCE', 1, 0),
('YOUR_SERVICE_MANAGEMENT', 'GENERIC', 'HOST', 1, 1);

-- Map commands to groups
INSERT INTO health_check.chatops_command_group_mapping
(`cmd_id`, `command_group_id`, `sequence`, `exec_mode`, `pdf_title`) VALUES
('YOUR_SERVICE_STATUS', 'YOUR_SERVICE_MONITORING', 1, 'SEQUENTIAL', 'Service Status Report'),
('YOUR_SERVICE_METRICS', 'YOUR_SERVICE_MONITORING', 2, 'SEQUENTIAL', 'Service Metrics Report'),
('YOUR_SERVICE_RESTART', 'YOUR_SERVICE_MANAGEMENT', 1, 'SEQUENTIAL', 'Service Restart Log');

-- //@UNDO
-- Rollback mappings first (due to foreign key constraints)
DELETE FROM health_check.chatops_command_group_mapping
WHERE command_group_id IN ('YOUR_SERVICE_MONITORING', 'YOUR_SERVICE_MANAGEMENT');

-- Rollback command groups
DELETE FROM health_check.chatops_command_group
WHERE id IN ('YOUR_SERVICE_MONITORING', 'YOUR_SERVICE_MANAGEMENT');

-- Rollback commands
DELETE FROM health_check.chatops_command
WHERE id IN ('YOUR_SERVICE_STATUS', 'YOUR_SERVICE_METRICS', 'YOUR_SERVICE_RESTART');
```

## ArgHandler Integration

### Creating Supporting ArgHandlers

If your command service requires custom arguments, create corresponding ArgHandlers:

#### 1. Add to CmdArgEnum

```java
// src/main/java/hk/org/ha/sc3/sybasechatops/constant/cmd/CmdArgEnum.java
public enum CmdArgEnum {
    // Existing arguments
    ARG_HOST,
    ARG_INSTANCE,

    // Your new arguments
    ARG_YOUR_SERVICE_ENDPOINT,
    ARG_YOUR_SERVICE_OPERATION
}
```

#### 2. Create ArgHandler

```java
package hk.org.ha.sc3.sybasechatops.component.arghandler.yourservice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class YourServiceEndpointArgHandler extends ICommandArgHandler {

    private YourServiceRepository yourServiceRepository;

    public YourServiceEndpointArgHandler(YourServiceRepository yourServiceRepository) {
        this.yourServiceRepository = yourServiceRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_YOUR_SERVICE_ENDPOINT;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        // Get context from session if needed
        String host = (String) httpSession.getAttribute(CmdArgEnum.ARG_HOST.name());

        // Fetch available endpoints for the host
        List<YourServiceEndpoint> endpoints = yourServiceRepository.findByHost(host);

        for (YourServiceEndpoint endpoint : endpoints) {
            hiddenValue.clear();
            hiddenValue.put(getCmdArgType().name(), endpoint.getUrl());

            try {
                String jsonStr = mapper.writeValueAsString(hiddenValue);
                Option option = Option.builder()
                    .text(endpoint.getDisplayName())
                    .value(jsonStr)
                    .nextLabel(msgChain + "|" + getCmdArgType())
                    .build();
                options.add(option);
            } catch (JsonProcessingException e) {
                log.error("Error processing JSON for endpoint: {}", endpoint.getUrl(), e);
            }
        }

        return options;
    }
}
```

#### 3. Add Argument Database Entries

```sql
-- Add argument definitions
INSERT INTO health_check.chatops_command_argument
(`id`, `key_name`, `description`, `arg_type`) VALUES
('your_service_endpoint_001', 'service_endpoint', 'Please select the service endpoint', 'ARG_YOUR_SERVICE_ENDPOINT'),
('your_service_operation_001', 'service_operation', 'Please select the operation', 'ARG_YOUR_SERVICE_OPERATION');

-- Map arguments to commands
INSERT INTO health_check.chatops_command_argument_mapping
(`cmd_id`, `argument_id`, `sequence`, `required`, `enabled`) VALUES
('YOUR_SERVICE_STATUS', 'your_service_endpoint_001', 1, 1, 1),
('YOUR_SERVICE_METRICS', 'your_service_endpoint_001', 1, 1, 1),
('YOUR_SERVICE_METRICS', 'your_service_operation_001', 2, 1, 1);
```

## Testing Your Command Service

### Unit Test Template

```java
package hk.org.ha.sc3.sybasechatops.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;

@ExtendWith(MockitoExtension.class)
class YourNewCommandServiceTest {

    @Mock
    private YourExternalApiClient apiClient;

    @Mock
    private YourConfigurationService configService;

    @InjectMocks
    private YourNewCommandService commandService;

    private Command testCommand;

    @BeforeEach
    void setUp() {
        testCommand = new Command();
        testCommand.setId("TEST_COMMAND");
        testCommand.setCommand("getStatus");
        testCommand.setCmdType(CmdTypeEnum.YOUR_NEW_TYPE);
        testCommand.setUser("admin");
        testCommand.setTimeout(30);
    }

    @Test
    void testGetCmdType() throws Exception {
        assertEquals(CmdTypeEnum.YOUR_NEW_TYPE, commandService.getCmdType());
    }

    @Test
    void testExecByCommand_Success() throws Exception {
        // Given
        String expectedResult = "Service is running";
        when(apiClient.getStatus(anyString(), anyString())).thenReturn(expectedResult);

        // When
        CommandResult result = commandService.execByCommand(testCommand);

        // Then
        assertEquals(0, result.getRc());
        assertEquals(expectedResult, result.getStdout());
        assertEquals("", result.getStderr());
        assertEquals("getStatus", result.getCommand());
    }

    @Test
    void testExecByCommand_Failure() throws Exception {
        // Given
        String errorMessage = "Connection failed";
        when(apiClient.getStatus(anyString(), anyString()))
            .thenThrow(new RuntimeException(errorMessage));

        // When
        CommandResult result = commandService.execByCommand(testCommand);

        // Then
        assertEquals(-1, result.getRc());
        assertEquals("", result.getStdout());
        assertTrue(result.getStderr().contains(errorMessage));
    }

    @Test
    void testExecByCommand_UnknownCommand() throws Exception {
        // Given
        testCommand.setCommand("unknownCommand");

        // When
        CommandResult result = commandService.execByCommand(testCommand);

        // Then
        assertEquals(-1, result.getRc());
        assertTrue(result.getStderr().contains("Unknown command"));
    }
}
```

### Integration Test Template

```java
package hk.org.ha.sc3.sybasechatops.integration;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import hk.org.ha.sc3.sybasechatops.service.CommandGroupService;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class YourNewCommandServiceIntegrationTest {

    @Autowired
    private CommandGroupService commandGroupService;

    @Test
    void testCommandGroupExecution() throws Exception {
        // Given
        String commandGroupId = "YOUR_SERVICE_MONITORING";
        String hostName = "test-host";
        String instanceName = "test-instance";
        HashMap<String, String> storeVariables = new HashMap<>();

        // When
        var results = commandGroupService.execByCommandGroupId(
            commandGroupId, hostName, instanceName, storeVariables);

        // Then
        assertNotNull(results);
        assertFalse(results.isEmpty());

        for (CommandResult result : results) {
            assertNotNull(result);
            // Add specific assertions based on expected results
        }
    }
}
```

## Best Practices and Guidelines

### 1. Error Handling

Always implement comprehensive error handling:

```java
@Override
public CommandResult execByCommand(Command command) throws Exception {
    long startTime = System.currentTimeMillis();

    try {
        // Validate inputs
        validateCommand(command);

        // Execute command
        String result = executeCommand(command);

        // Calculate execution time
        double elapsedTime = (System.currentTimeMillis() - startTime) / 1000.0;

        return CommandResult.builder()
            .stdout(result)
            .stderr("")
            .rc(0)
            .elapsedTime(elapsedTime)
            .command(command.getCommand())
            .build();

    } catch (ValidationException e) {
        log.warn("Command validation failed: {}", e.getMessage());
        return createErrorResult(command, "Validation error: " + e.getMessage(), -2);
    } catch (TimeoutException e) {
        log.error("Command execution timed out: {}", command.getCommand());
        return createErrorResult(command, "Command execution timed out", -3);
    } catch (Exception e) {
        log.error("Unexpected error executing command: {}", command.getCommand(), e);
        return createErrorResult(command, "Unexpected error: " + e.getMessage(), -1);
    }
}

private CommandResult createErrorResult(Command command, String errorMessage, int returnCode) {
    double elapsedTime = (System.currentTimeMillis() - startTime) / 1000.0;
    return CommandResult.builder()
        .stdout("")
        .stderr(errorMessage)
        .rc(returnCode)
        .elapsedTime(elapsedTime)
        .command(command.getCommand())
        .build();
}
```

### 2. Configuration Management

Use Spring's configuration properties for external settings:

```java
@ConfigurationProperties(prefix = "your-service")
@Component
public class YourServiceConfig {
    private String baseUrl;
    private int timeout;
    private String apiKey;
    private boolean sslEnabled;

    // Getters and setters
}
```

```yaml
# application.yml
your-service:
  base-url: https://api.yourservice.com
  timeout: 30
  api-key: ${YOUR_SERVICE_API_KEY:default-key}
  ssl-enabled: true
```

### 3. Logging Standards

Follow consistent logging patterns:

```java
@Slf4j
@Service
public class YourNewCommandService implements ICommand {

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        log.info("Executing {} command: {} for host: {}, instance: {}",
                getCmdType(), command.getCommand(), command.getHost(), command.getInstance());

        try {
            // Command execution
            String result = executeCommand(command);
            log.debug("Command executed successfully. Result length: {}", result.length());
            return createSuccessResult(result, command);

        } catch (Exception e) {
            log.error("Failed to execute {} command: {} - Error: {}",
                     getCmdType(), command.getCommand(), e.getMessage(), e);
            throw e;
        }
    }
}
```

### 4. Performance Considerations

- **Timeouts**: Always respect command timeout settings
- **Resource Management**: Properly close connections and resources
- **Async Operations**: Consider async execution for long-running commands
- **Caching**: Cache frequently accessed configuration or metadata

```java
@Service
public class YourNewCommandService implements ICommand {

    @Autowired
    private TaskExecutor taskExecutor;

    @Cacheable(value = "service-config", key = "#host")
    public ServiceConfig getServiceConfig(String host) {
        // Expensive configuration lookup
        return configService.getConfig(host);
    }

    private String executeWithTimeout(Command command) throws Exception {
        Future<String> future = taskExecutor.submit(() -> {
            return actualCommandExecution(command);
        });

        try {
            return future.get(command.getTimeout(), TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            throw new RuntimeException("Command execution timed out after " + command.getTimeout() + " seconds");
        }
    }
}
```

### 5. Security Considerations

- **Input Validation**: Always validate command parameters
- **Authentication**: Implement proper authentication for external services
- **Secrets Management**: Use Spring's property encryption for sensitive data
- **Audit Logging**: Log all command executions for security auditing

```java
private void validateCommand(Command command) throws ValidationException {
    if (command == null) {
        throw new ValidationException("Command cannot be null");
    }

    if (StringUtils.isBlank(command.getCommand())) {
        throw new ValidationException("Command string cannot be empty");
    }

    // Validate against allowed commands
    if (!ALLOWED_COMMANDS.contains(command.getCommand())) {
        throw new ValidationException("Command not allowed: " + command.getCommand());
    }

    // Validate host format
    if (!isValidHost(command.getHost())) {
        throw new ValidationException("Invalid host format: " + command.getHost());
    }
}
```

## Deployment and Verification

### 1. Pre-deployment Checklist

- [ ] CmdTypeEnum updated with new command type
- [ ] Command service class implements ICommand interface
- [ ] Service is annotated with @Service for Spring discovery
- [ ] Database migration script created and tested
- [ ] ArgHandlers created for custom arguments (if needed)
- [ ] Unit tests written and passing
- [ ] Integration tests written and passing
- [ ] Configuration properties defined
- [ ] Logging implemented consistently
- [ ] Error handling covers all scenarios

### 2. Deployment Steps

1. **Code Deployment**: Deploy the Java application with your new service
2. **Database Migration**: Run MyBatis migration to add database entries
3. **Configuration Update**: Update application properties if needed
4. **Service Verification**: Verify the service is registered and discoverable

### 3. Verification Commands

```bash
# Check if service is registered
curl -X GET "http://localhost:8080/actuator/beans" | grep -i "yourNewCommandService"

# Test command execution via API
curl -X POST "http://localhost:8080/api/command/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "commandGroupId": "YOUR_SERVICE_MONITORING",
    "hostName": "test-host",
    "instanceName": "test-instance"
  }'
```

### 4. Troubleshooting Common Issues

#### Service Not Found
- Verify @Service annotation is present
- Check package scanning configuration
- Ensure class is in correct package structure

#### Database Errors
- Verify migration script syntax
- Check foreign key constraints
- Ensure enum values match exactly

#### Command Execution Failures
- Check logs for detailed error messages
- Verify external service connectivity
- Validate command parameters and format

## Example: Complete Implementation

Here's a complete example implementing a REST API command service:

```java
package hk.org.ha.sc3.sybasechatops.service;

import java.time.Duration;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class RestApiCommandService implements ICommand {

    private final WebClient webClient;

    @Value("${rest-api.base-url}")
    private String baseUrl;

    @Value("${rest-api.api-key}")
    private String apiKey;

    public RestApiCommandService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
            .baseUrl(baseUrl)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader("X-API-Key", apiKey)
            .build();
    }

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        log.info("Executing REST API command: {} for host: {}",
                command.getCommand(), command.getHost());

        long startTime = System.currentTimeMillis();

        try {
            String result = executeRestCommand(command);
            double elapsedTime = (System.currentTimeMillis() - startTime) / 1000.0;

            return CommandResult.builder()
                .stdout(result)
                .stderr("")
                .rc(0)
                .elapsedTime(elapsedTime)
                .command(command.getCommand())
                .build();

        } catch (Exception e) {
            log.error("REST API command failed: {}", command.getCommand(), e);
            double elapsedTime = (System.currentTimeMillis() - startTime) / 1000.0;

            return CommandResult.builder()
                .stdout("")
                .stderr("Error: " + e.getMessage())
                .rc(-1)
                .elapsedTime(elapsedTime)
                .command(command.getCommand())
                .build();
        }
    }

    @Override
    public CmdTypeEnum getCmdType() throws Exception {
        return CmdTypeEnum.REST_API;
    }

    private String executeRestCommand(Command command) throws Exception {
        String endpoint = buildEndpoint(command);

        return webClient.get()
            .uri(endpoint)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(Duration.ofSeconds(command.getTimeout()))
            .onErrorMap(WebClientResponseException.class, ex ->
                new RuntimeException("API call failed with status: " + ex.getStatusCode()))
            .block();
    }

    private String buildEndpoint(Command command) {
        Map<String, String> variables = command.getStoreVariables();
        String host = command.getHost();
        String instance = command.getInstance();

        switch (command.getCommand()) {
            case "getStatus":
                return String.format("/api/v1/hosts/%s/status", host);
            case "getMetrics":
                return String.format("/api/v1/hosts/%s/instances/%s/metrics", host, instance);
            case "restart":
                return String.format("/api/v1/hosts/%s/instances/%s/restart", host, instance);
            default:
                throw new IllegalArgumentException("Unknown command: " + command.getCommand());
        }
    }
}
```

This comprehensive guide provides everything needed to implement a new command service in the SC3 ChatOps system, from initial setup through deployment and verification.
```
```
