<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
  <meta charset="UTF-8" />
  <style th:inline="css"></style>
</head>

<body style="font-family: monospace;white-space: pre-wrap;">
  <p th:text="|Requester: ${requester}|"></p>
  <p th:text="|Request Time: ${dateTime}|"></p>

  <table border="1" style="width:300px">
    <thead>
      <tr>
        <th> Host </th>
        <th> Instance </th>
      </tr>
    </thead>
    <tbody>
      <tr th:each="instance : ${instanceList}">
        <td data-th-text="${instance.getId().getHost()}"></td>
        <td data-th-text="${instance.getId().getInstance()}"></td>
      </tr>
    </tbody>
  </table>

</body>

</html>