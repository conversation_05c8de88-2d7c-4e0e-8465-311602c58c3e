---
sidebar_position: 5
---

# How to Add a New Command Group Button

## Overview

This guide explains how to create a new command group button that integrates in the SC3 ChatOps. Command group buttons provide quick access to related commands in the chat interface.

## Prerequisites

Before starting, ensure you understand:
- [How to Implement a New ArgHandler](./How-to-Implement-New-ArgHandler)
- [How to Implement Command Service](./How-to-Implement-Command-Service)
- Basic knowledge of Spring Boot, MyBatis, and the ChatOps architecture

## Architecture Overview

The integration between command group buttons and ArgHandlers follows this flow:

```mermaid
graph TD
    subgraph UI ["User Interface"]
        direction TB
        Chat[HA Chatroom]
        Button[Command Group Button]
    end

    subgraph Backend ["Backend Processing"]
        direction TB
        Controller[BaseMenuControllerV2]
        ArgHandler@{ shape: procs, label: "ArgHandler Implementation"}
        HealthController[BaseHealthCheckControllerV2]
        Service[Command Service]
    end

    subgraph DB ["Database"]
        direction TB
        CmdGroup[chatops_command_group]
        CmdGroupMapping[chatops_command_group_mapping]
        Command[chatops_command]
        CmdArg[chatops_command_argument]
        CmdArgMapping[chatops_command_argument_mapping]
    end


    Chat -->|User clicks| Button
    Button -->|Sends message| Controller
    Controller -->|Finds ArgHandler| ArgHandler
    ArgHandler -->|Provides options| Controller
    Controller -->|All args collected| HealthController
    HealthController -->|Executes command| Service

    Controller -.->|Reads config| CmdGroup
    Controller -.->|Reads mapping| CmdGroupMapping
    Service -.->|Reads commands| Command
    ArgHandler -.->|Reads arguments| CmdArg
    ArgHandler -.->|Reads arg mapping| CmdArgMapping
```

## Step-by-Step Implementation

### Step 1: Create the ArgHandler (if needed)

If your command group requires dynamic argument selection and related handler is not existing, you'll need to create a new ArgHandler. Refer to [How to Implement a New ArgHandler](./How-to-Implement-New-ArgHandler) for detailed implementation steps.

### Step 2: Implement Command Service (if needed)

If your command group requires new command type, you'll need to create a new command service. Refer to [How to Implement a New ArgHandler](./How-to-ImplementCommand-Service) for detailed implementation steps.

### Step 3: Create Database Migration Script

#### Database Schema Reference

##### Required Tables and Relationships

The ArgHandler integration requires entries in these key tables:

###### 1. chatops_command_group
Defines the command group that appears as a button:
- `id`: Unique identifier for the command group
- `cmd_type`: Type of command group (HOST, INSTANCE, PROJECT)
- `db_type`: Database type filter (ORACLE, MYSQL, etc.)
- `enabled`: Whether the group is active
- `is_approval`: Whether commands require approval

###### 2. chatops_command_group_mapping
Links commands to command groups:
- `id`: Unique identifier for the command group mapping
- `command_id`: References chatops_command.id
- `command_group_id`: References chatops_command_group.id
- `pdf_title`: Title for PDF output
- `exec_mode`: Execution mode (SEQUENTIAL, PARALLEL)
- `sequence`: Order of execution within the group

Create a MyBatis migration script following the naming convention:

```sql
-- mybatis/db/migration/scripts/YYYYMMDDNNN_TICKET-ID_ADD_YOUR_SERVICE_SUPPORT.sql
-- Description: Add support for Your Service commands with ArgHandler integration

-- 1. Create command group
INSERT INTO `health_check`.`chatops_command_group` 
(`id`, `cmd_type`, `db_type`, `enabled`, `is_approval`) VALUES
('YOUR_SERVICE_GROUP', 'INSTANCE', 'GENERIC', 1, 0);

-- 2. Create commands if needed. Please refer to step 2.
-- INSERT INTO `health_check`.`chatops_command` 
-- (`id`, `command`, `enabled`, `user`, `timeout`, `cmd_type`, `return_type`) VALUES
-- ('YOUR_SERVICE_STATUS', 'your-service-status', 1, 'chatops', 300, 'YOUR_SERVICE', 'TEXT'),
-- ('YOUR_SERVICE_RESTART', 'your-service-restart', 1, 'chatops', 600, 'YOUR_SERVICE', 'TEXT');

-- 3. Create command arguments
-- INSERT INTO `health_check`.`chatops_command_argument` 
-- (`id`, `key_name`, `description`, `arg_type`) VALUES
-- ('your_service_arg', 'service', 'Please select the service', 'ARG_YOUR_SERVICE'),
-- ('host_arg', 'host', 'Please select the host', 'ARG_HOST');

-- 4. Create command argument mappings if needed. Please refer to step 3.
-- INSERT INTO `health_check`.`chatops_command_argument_mapping` 
-- (`cmd_id`, `argument_id`, `sequence`, `required`, `enabled`) VALUES
-- ('YOUR_SERVICE_STATUS', 'host_arg', 1, 1, 1),
-- ('YOUR_SERVICE_STATUS', 'your_service_arg', 2, 1, 1),
-- ('YOUR_SERVICE_RESTART', 'host_arg', 1, 1, 1),
-- ('YOUR_SERVICE_RESTART', 'your_service_arg', 2, 1, 1);

-- 5. Create command group mappings
INSERT INTO `health_check`.`chatops_command_group_mapping` 
(`id`, `command_id`, `command_group_id`, `sequence`, `exec_mode`, `pdf_title`) VALUES
(100, 'YOUR_SERVICE_STATUS', 'YOUR_SERVICE_GROUP', 1, 'SEQUENTIAL', 'Service Status Report'),
(101, 'YOUR_SERVICE_RESTART', 'YOUR_SERVICE_GROUP', 2, 'SEQUENTIAL', 'Service Restart Log');

-- //@UNDO
-- Cleanup statements for rollback

-- Delete the command arg & command if needed.
-- DELETE FROM `health_check`.`chatops_command_argument_mapping` 
-- WHERE `cmd_id` IN ('YOUR_SERVICE_STATUS', 'YOUR_SERVICE_RESTART');

-- DELETE FROM `health_check`.`chatops_command_argument` 
-- WHERE `id` IN ('your_service_arg', 'host_arg');

-- DELETE FROM `health_check`.`chatops_command` 
-- WHERE `id` IN ('YOUR_SERVICE_STATUS', 'YOUR_SERVICE_RESTART');

DELETE FROM `health_check`.`chatops_command_group` 
WHERE `id` = 'YOUR_SERVICE_GROUP';

DELETE FROM `health_check`.`chatops_command_group_mapping` 
WHERE `id` IN (100, 101);

```

## Testing Your Implementation

### 1. Unit Tests for ArgHandler

```java
// src/test/java/hk/org/ha/sc3/sybasechatops/component/arghandler/YourServiceArgHandlerTest.java
@ExtendWith(MockitoExtension.class)
class YourServiceArgHandlerTest {

    @Mock
    private YourServiceRepository yourServiceRepository;

    @Mock
    private HttpSession httpSession;

    @InjectMocks
    private YourServiceArgHandler argHandler;

    @Test
    void shouldReturnCorrectCmdArgType() {
        assertEquals(CmdArgEnum.ARG_YOUR_SERVICE, argHandler.getCmdArgType());
    }

    @Test
    void shouldReturnOptionsForTeam() {
        // Given
        when(httpSession.getAttribute(SessionAttrEnum.TEAM.name())).thenReturn("TEST_TEAM");
        List<YourServiceEntity> services = Arrays.asList(
            new YourServiceEntity("service1", "Service 1"),
            new YourServiceEntity("service2", "Service 2")
        );
        when(yourServiceRepository.findByTeam("TEST_TEAM")).thenReturn(services);

        CommandArgument argument = new CommandArgument();
        String msgChain = "@itbot|SC3 Database Health Check|session123|GENERIC|YOUR_SERVICE_GROUP";

        // When
        List<Option> options = argHandler.getOptions(argument, httpSession, msgChain);

        // Then
        assertEquals(2, options.size());
        assertEquals("Service 1", options.get(0).getText());
        assertEquals("Service 2", options.get(1).getText());
    }
}
```

### 2. Integration Testing

Test the complete flow from button click to command execution:

1. **Database Setup**: Ensure your migration script has been applied through MyBatis migration
2. **Spring Context**: Verify your ArgHandler is registered as a Spring component
3. **Chat Interface**: Test the button appears in the chat interface
4. **Argument Flow**: Verify arguments are presented in the correct sequence
5. **Command Execution**: Confirm commands execute with the selected arguments

### 3. Manual Testing Steps

1. Start the ChatOps application
2. Access the chat interface
3. Type `@itbot` to initialize
4. Verify your command group button appears
5. Click the button and follow the argument selection flow
6. Confirm the command executes with the selected arguments

## Troubleshooting

### Common Issues

1. **Arguments Not Appearing**
   - Verify database entries in `chatops_command_argument`
   - Check `chatops_command_argument_mapping` for correct sequence
   - Ensure `enabled` flag is set to 1

3. **Command Not Executing**
   - Verify command service implements `ICommandService`
   - Check `getCmdType()` returns the correct enum value
   - Ensure command is registered in the database

4. **Button Not Visible**
   - Check `chatops_command_group` has `enabled = 1`
   - Verify user has access to the specified `db_type`
   - Ensure command group mapping exists
