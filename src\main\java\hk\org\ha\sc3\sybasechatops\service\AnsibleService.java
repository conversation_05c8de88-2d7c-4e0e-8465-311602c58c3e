package hk.org.ha.sc3.sybasechatops.service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.config.AnsibleConfig;
import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.AnsibleJobReturn;
import hk.org.ha.sc3.sybasechatops.model.AnsibleLaunchJobReq;
import hk.org.ha.sc3.sybasechatops.model.AnsibleListReturn;
import hk.org.ha.sc3.sybasechatops.model.db.AnsibleInventory;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.repository.AnsibleInventoryRepository;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
@Service
public class AnsibleService implements ICommand {
    private HttpClient sslHttpClient;
    private WebClient ansibleClient;
    private AnsibleInventoryRepository ansibleInventoryRepository;
    private WebhookService webhookService;
    private int interval;

    public AnsibleService(AnsibleConfig ansibleConfig, HttpClient sslHttpClient,
            AnsibleInventoryRepository ansibleInventoryRepository, WebhookService webhookService) {
        this.sslHttpClient = sslHttpClient;
        this.ansibleClient = WebClient.builder().baseUrl(ansibleConfig.getBaseUrl())
                .defaultHeaders(headers -> headers.setBasicAuth(ansibleConfig.getUser(), ansibleConfig.getPassword()))
                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient)).build();
        this.ansibleInventoryRepository = ansibleInventoryRepository;
        this.interval = ansibleConfig.getInterval();
        this.webhookService = webhookService;
    }

    private boolean isJobDone(String jobStatusStr) {
        try {
            String jobStatus = new ObjectMapper().readTree(jobStatusStr).get("status").asText();
            return jobStatus.equals("successful") || jobStatus.equals("failed") || jobStatus.equals("canceled");
        } catch (JsonProcessingException e) {
            log.error("Error parsing job status: {}", e.getMessage());
            return false;
        }
    }

    public WebClient getAnsibleClient() {
        return this.ansibleClient;
    }

    public Mono<AnsibleListReturn> getJobIdByJobTemplateMono(String jobTemplate) {
        String path = String.format("v2/job_templates/?name=%s", jobTemplate);
        return getAnsibleClient().get().uri(path).retrieve().bodyToMono(AnsibleListReturn.class);
    }

    public Mono<AnsibleJobReturn> launchJobTemplateByIdMono(int jobTemplateId, int inventoryId, String[] jobTags,
            String[] skipTags) {
        log.info("Job Template [{}] started in Inventory [{}].", jobTemplateId, inventoryId);
        log.info("Job Tags [{}] || Skip Tags [{}]", jobTags, skipTags);
        String path = String.format("v2/job_templates/%s/launch/", String.valueOf(jobTemplateId));
        return getAnsibleClient()
                .post().uri(path).contentType(MediaType.APPLICATION_JSON).bodyValue(AnsibleLaunchJobReq.builder()
                        .inventory(inventoryId).jobTags(jobTags).skipTags(skipTags).build())
                .retrieve().bodyToMono(AnsibleJobReturn.class);
    }

    public Mono<String> getJobStatusMono(int jobId) {
        log.debug("Fetching Job Status of Job [{}]", jobId);
        String path = String.format("v2/jobs/%s/", String.valueOf(jobId));
        return getAnsibleClient().get().uri(path).retrieve().bodyToMono(String.class);
    }

    public Mono<String> getJobStdoutMono(int jobId) {
        log.debug("Fetching Job Output of Job [{}]", jobId);
        String path = String.format("v2/jobs/%s/stdout/?format=ansi", String.valueOf(jobId));
        return getAnsibleClient().get().uri(path).retrieve().bodyToMono(String.class);
    }

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        CommandResult commandResult = CommandResult.builder().rc(0).command(command.getCommand()).stdout("Ansible command is executed. The result will be sent via webhook.")
                .build();
        AtomicReference<StopWatch> stopWatchRef = new AtomicReference<>(new StopWatch());
        AtomicReference<AnsibleJobReturn> jobRef = new AtomicReference<>();

        Optional<AnsibleInventory> opt = this.ansibleInventoryRepository.findByHostname(command.getHost());
        int inventoryId = opt.map(AnsibleInventory::getId).get().intValue();

        LocalDateTime dateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
        String webhookFileName = String.format("ansible_webhook_%s(%s@%s).pdf", dateTime.format(formatter),
                command.getHost(), command.getCommand());

        String webhookMsg = String.format("Ansible webhook result (Host: %s, Command: %s)", command.getHost(),
                command.getCommand());

        stopWatchRef.get().start();

        getJobIdByJobTemplateMono(command.getCommand()).flatMap(jobId -> {
            return launchJobTemplateByIdMono(jobId.getResults().get(0).getId(), inventoryId, null, null)
                    .doOnNext(jobRef::set);
        }).flatMap(launchJobObj -> {
            return getJobStatusMono(jobRef.get().getJob()).expand(resp -> {
                if (isJobDone(resp)) {
                    return Mono.empty();
                }
                return Mono.just(resp).delayElement(Duration.ofSeconds(interval))
                        .then(getJobStatusMono(launchJobObj.getJob()));
            }).filter(this::isJobDone).next().timeout(Duration.ofMinutes(60));
        }).flatMap(status -> getJobStdoutMono(jobRef.get().getJob())).flatMap(ansibleStdout -> {
            stopWatchRef.get().stop();
            CommandResult cmdResult = CommandResult.builder().stdout(ansibleStdout).stderr("").rc(0)
                    .pdfTitle("Ansible Automation Platform Result")
                    .elapsedTime(stopWatchRef.get().getTotalTimeSeconds()).commandObj(command)
                    .command(command.getCommand()).returnType(CmdReturnEnum.SSH).build();
            return this.webhookService.sendHealthCheckPdf(webhookFileName, dateTime, webhookMsg, cmdResult);
        }).subscribe();

        log.info("Ansible command executed: {}", command.getCommand());
        return commandResult;
    }

    @Override
    public CmdTypeEnum getCmdType() throws Exception {
        return CmdTypeEnum.ANSIBLE;
    }
}