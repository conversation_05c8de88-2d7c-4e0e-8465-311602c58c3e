package hk.org.ha.sc3.sybasechatops.controller.oracle;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.config.NotificationConfig;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseMenuController;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.SimpleMenu;
import hk.org.ha.sc3.sybasechatops.model.TextInput;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;

@RestController
@RequestMapping("oracle")
public class OracleMenuController extends BaseMenuController {

        private final TextInput oraInstanceInput;
        private final TextInput oraHostInput;
        private final TextInput oraProjInput;
        private final SimpleMenu oracleMenu;

        public OracleMenuController(DatabaseRepository databaseRepository,
                        CommandGroupRepository commandGroupRepository, NotificationConfig notificationConfig) {
                super(databaseRepository, commandGroupRepository, OracleHealthCheckController.class,
                                notificationConfig);
                oraInstanceInput = TextInput.builder()
                                .msg("Please input Oracle instance name wildcast (e.g. %dbsreps1%)")
                                .store("{ora_instance}")
                                .nextLabel("@itbot|Oracle|Instance Search|Instance List").build();
                oraHostInput = TextInput.builder()
                                .msg("Please input Oracle host name wildcast (e.g. %dbmoratst11c%)")
                                .store("{ora_host}")
                                .nextLabel("@itbot|Oracle|Host Search|Host List").build();
                oraProjInput = TextInput.builder()
                                .msg("Please input Oracle project code wildcast (e.g. %dbs%)")
                                .store("{ora_proj}")
                                .nextLabel("@itbot|Oracle|Project Search|Project List").build();

                final String[] oracleOptions = { "Host Search", "Instance Search", "Project Search" };
                oracleMenu = SimpleMenu.builder().msg("Please select an instance below")
                                .options(oracleOptions).statusCode(200)
                                .build();
        }

        @Override
        public DatabaseTypeEnum getType() {
                return DatabaseTypeEnum.ORACLE;
        }

        @Override
        public ChatopsRespBase getBaseResponse(String msgChain, String token) {
                switch (msgChain) {
                        case "@itbot|Oracle":
                                return this.oracleMenu;
                        case "@itbot|Oracle|Instance Search":
                                return this.oraInstanceInput;
                        case "@itbot|Oracle|Host Search":
                                return this.oraHostInput;
                        case "@itbot|Oracle|Project Search":
                                return this.oraProjInput;
                        default:
                                return super.getUnknownReply();
                }
        }
}
