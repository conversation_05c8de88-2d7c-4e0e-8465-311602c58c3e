package hk.org.ha.sc3.sybasechatops.model.db;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdReturnEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class CommandResult {
    private final String stdout;
    private final String stderr;
    private final int rc;
    private final String file;
    private String pdfTitle;
    private double elapsedTime;
    private String command;
    private CmdReturnEnum returnType;
    private Command commandObj;

    /*
     * add a method to get max line length from stdout and stderr, split by new line
     */
    public int getMaxLineLength() {
        int stdoutMax = getMaxLineLength(stdout);
        int stderrMax = getMaxLineLength(stderr);
        /* return max between stdoutMax and stderrMax */
        return Math.max(stdoutMax, stderrMax);
    }

    private int getMaxLineLength(String content) {
        int maxLineLength = 0;
        String[] lines = content.split("\n");
        for (String line : lines) {
            int lineLength = line.length();
            if (lineLength > maxLineLength) {
                maxLineLength = lineLength;
            }
        }
        return maxLineLength;
    }

    public int getLineNum() {
        int stdoutNum = this.stdout.split("\n").length;
        int stderrNum = this.stderr.split("\n").length;
        return stdoutNum + stderrNum;
    }

}