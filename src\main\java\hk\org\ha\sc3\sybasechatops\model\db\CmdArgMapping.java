package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.*;

import org.hibernate.annotations.Type;

import hk.org.ha.sc3.sybasechatops.model.db.id.CmdArgMappingId;

import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "chatops_command_argument_mapping", schema = "health_check", catalog = "health_check")
@IdClass(CmdArgMappingId.class) // Use IdClass for composite key
@Getter
@Setter
public class CmdArgMapping {

    @Id
    @ManyToOne(fetch = FetchType.LAZY) // Use LAZY fetching typically
    @JoinColumn(name = "cmd_id", referencedColumnName = "id")
    private Command command;

    @Id
    @ManyToOne(fetch = FetchType.LAZY) // Use LAZY fetching typically
    @JoinColumn(name = "argument_id", referencedColumnName = "id") // Assuming CommandArgument has an 'id' field
    private CommandArgument commandArgument;
    
    @Type(type = "org.hibernate.type.NumericBooleanType")
    private boolean required;
    
    @Type(type = "org.hibernate.type.NumericBooleanType")
    private boolean enabled;

}
