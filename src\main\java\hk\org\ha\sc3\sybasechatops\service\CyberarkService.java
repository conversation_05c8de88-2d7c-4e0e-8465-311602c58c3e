package hk.org.ha.sc3.sybasechatops.service;

import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;

import hk.org.ha.sc3.sybasechatops.config.CyberarkConfig;
import hk.org.ha.sc3.sybasechatops.exception.MultipleAccountException;
import hk.org.ha.sc3.sybasechatops.exception.NoAccountException;
import hk.org.ha.sc3.sybasechatops.model.cyberark.AccountsResponse;
import hk.org.ha.sc3.sybasechatops.model.cyberark.Logon;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
@Service
public class CyberarkService {

    private CyberarkConfig cyberarkConfig;
    private HttpClient sslHttpClient;
    private WebClient authCyberarkClient;
    private WebClient logonCyberarkClient;

    public CyberarkService(CyberarkConfig cyberarkConfig, HttpClient sslHttpClient) {
        this.cyberarkConfig = cyberarkConfig;
        this.sslHttpClient = sslHttpClient;

        this.authCyberarkClient = WebClient.builder().baseUrl(cyberarkConfig.getBaseUrl())
                .defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(ExchangeFilterFunction.ofRequestProcessor(
                        (ClientRequest request) -> {
                            return getTokenMono()
                                    .map((token) -> {
                                        return ClientRequest.from(request)
                                                .header("Authorization", token).build();
                                    });

                        }))
                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient)).build();

        this.logonCyberarkClient = WebClient.builder().baseUrl(cyberarkConfig.getBaseUrl())
                .defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient)).build();
    }

    public WebClient getLogonClient() {
        return this.logonCyberarkClient;
    }

    public WebClient getAuthCyberarkClient() {
        return this.authCyberarkClient;
    }

    private Mono<String> getTokenMono() {
        Logon cyberarkLogon = Logon.builder().username(cyberarkConfig.getUser()).password(cyberarkConfig.getPassword())
                .build();

        Mono<String> tokenMono = getLogonClient().post().uri("/auth/ldap/Logon")
                .body(Mono.just(cyberarkLogon), Logon.class).retrieve()
                .bodyToMono(String.class)
                .map((token) -> token.replace("\"", ""))
                .doOnError(throwable -> log.error("Failed when getting cyberark token",
                        throwable))
                .onErrorResume(err -> Mono.empty());
        return tokenMono;
    }

    public Mono<AccountsResponse> getAccount(String searchKey) {
        return getAuthCyberarkClient().get()
                .uri(uriBuilder -> uriBuilder.path("/Accounts").queryParam("search", searchKey).build())
                .retrieve()
                .bodyToMono(AccountsResponse.class)
                .doOnError(throwable -> log.error("Failed when getting account list",
                        throwable));
    }

    public Mono<String> getPasswordFromId(String accountId) {
        return getAuthCyberarkClient().post()
                .uri("Accounts/{accountId}/Password/Retrieve", accountId)
                .retrieve()
                .bodyToMono(String.class)
                .map(password -> password.replace("\"", ""))
                .doOnError(throwable -> log.error("Failed when getting account list",
                        throwable));
    }

    public Mono<String> getAccountPassword(String username, String host) {
        return getAccountPassword(String.format("%s %s preapproval", username, host));
    }

    public Mono<String> getAccountPassword(String searchKey) {
        return this.getAccount(searchKey).doOnNext(accountsResponse -> {
            if (accountsResponse.getCount() == 0) {
                throw new NoAccountException(searchKey);
            } else if (accountsResponse.getCount() > 1) {
                throw new MultipleAccountException(searchKey);
            }
        }).flatMap(accountsResponse -> this.getPasswordFromId(accountsResponse.getValue().get(0).getId()));
    }
}
