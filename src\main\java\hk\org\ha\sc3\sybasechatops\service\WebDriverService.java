package hk.org.ha.sc3.sybasechatops.service;

import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;

import org.springframework.stereotype.Service;
import hk.org.ha.sc3.sybasechatops.config.WebDriverConfig;

import org.openqa.selenium.Dimension;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;

@Service
public class WebDriverService {

    private WebDriverConfig driverConfig;

    public WebDriverService(WebDriverConfig driverConfig) {
        this.driverConfig = driverConfig;
    }

    public FirefoxDriver firefoxDriver() {
        FirefoxOptions options = new FirefoxOptions();
        options.setBinary(this.driverConfig.getBinaryPath());
        System.setProperty("webdriver.gecko.driver", this.driverConfig.getLocation());
        if (this.driverConfig.isHeadless()) {
            options.addArguments("--headless");
            options.addArguments("--disable-gpu");
        }
        FirefoxDriver driver = new FirefoxDriver(options);

        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(30));
        driver.manage().window()
                .setSize(new Dimension(this.driverConfig.getWindowWidth(), this.driverConfig.getWindowHeight()));
        return driver;
    }

    public String takeSnapShot(FirefoxDriver webdriver) throws Exception {
        String pathStr = "./screenshot/";
        Path path = Paths.get(pathStr);

        TakesScreenshot scrShot = ((TakesScreenshot) webdriver);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
        }
        File srcFile = scrShot.getScreenshotAs(OutputType.FILE);

        String fileName = (new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss_SSS")).format(new Date()) + ".png";
        File outputFile = new File(pathStr, fileName);
        Files.copy(srcFile.toPath(), outputFile.toPath());

        return outputFile.getAbsolutePath();
    }

}