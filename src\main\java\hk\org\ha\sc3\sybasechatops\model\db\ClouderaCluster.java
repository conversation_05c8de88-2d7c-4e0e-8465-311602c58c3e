package hk.org.ha.sc3.sybasechatops.model.db;

import java.util.List;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_cloudera_cluster", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class ClouderaCluster {
    @Id
    private int clusterId;

    private String apiHost;

    private String apiVersion;

    private String clusterName;

    private String user;

    private String password;

    @OneToMany(mappedBy = "cluster")
    private List<ClouderaComponent> components;
}
