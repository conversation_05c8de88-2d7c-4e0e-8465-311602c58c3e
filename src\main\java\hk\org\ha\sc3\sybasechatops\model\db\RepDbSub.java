package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import hk.org.ha.sc3.sybasechatops.interfaces.IButtonView;
import hk.org.ha.sc3.sybasechatops.model.db.id.RepDbSubId;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_repserver_dbsub", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class RepDbSub implements IButtonView {
    @EmbeddedId
    private RepDbSubId id;

    @Override
    public String getButtonView() {
        return String.format("%s|%s", this.id.getRds(), this.id.getRdb());
    }
}
