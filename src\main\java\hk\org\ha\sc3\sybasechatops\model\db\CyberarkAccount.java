package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_cyberark_account", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class CyberarkAccount {
    @Id
    private Integer id;
    private String user;
    private String host;
}