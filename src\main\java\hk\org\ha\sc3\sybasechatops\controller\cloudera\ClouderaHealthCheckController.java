package hk.org.ha.sc3.sybasechatops.controller.cloudera;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseHealthCheckController;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.CommandGroupService;
import hk.org.ha.sc3.sybasechatops.service.PDFService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/cloudera/health_check")
public class ClouderaHealthCheckController extends BaseHealthCheckController {

    public ClouderaHealthCheckController(PDFService pdfService,
            DatabaseRepository databaseRepository, CommandGroupService commandGroupService) {
        super(pdfService, databaseRepository, commandGroupService);
    }

    @Override
    public DatabaseTypeEnum getType() {
        return DatabaseTypeEnum.CLOUDERA;
    }
}
