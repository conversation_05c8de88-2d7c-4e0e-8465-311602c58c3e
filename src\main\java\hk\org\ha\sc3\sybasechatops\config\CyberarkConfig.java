package hk.org.ha.sc3.sybasechatops.config;

import javax.net.ssl.SSLException;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.Getter;
import lombok.Setter;
import reactor.netty.http.client.HttpClient;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "cyberark")
public class CyberarkConfig {
    private String baseUrl;
    private int requestTimeout;
    private String user;
    private String password;

    @Bean
    public HttpClient sslHttpClient() throws SSLException {
        SslContext sslContext = SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();
        HttpClient sslHttpClient = HttpClient.create().secure(t -> t.sslContext(sslContext));
        return sslHttpClient;
    }

}
