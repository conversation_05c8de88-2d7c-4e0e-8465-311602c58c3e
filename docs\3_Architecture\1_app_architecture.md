---
sidebar_position: 3
---

# Architecture Overview

## Environment Architecture

The SC3 ChatOps system is deployed across multiple environments with primary and secondary server configurations for high availability.

### Architecture Graphs

#### Development Environment

```mermaid
graph LR
    DEV_PRIMARY["dbsoravmctst21a:8081<br/>Primary DEV"]
    DEV_SECONDARY["dbsoravmctst21b:8082<br/>Secondary DEV"]

    DEV_PRIMARY <-.-> DEV_SECONDARY

    style DEV_PRIMARY fill:#e1f5fe
    style DEV_SECONDARY fill:#e1f5fe
```

#### Development Environment (V2 Menu)

```mermaid
graph LR
    DEV_V2_PRIMARY["dbsoravmctst21a:8083<br/>Primary V2 Menu DEV"]
    DEV_V2_SECONDARY["dbsoravmctst21b:8084<br/>Secondary V2 Menu DEV"]

    DEV_V2_PRIMARY <-.-> DEV_V2_SECONDARY

    style DEV_V2_PRIMARY fill:#e8f5e8
    style DEV_V2_SECONDARY fill:#e8f5e8
```

#### SIT Environment

```mermaid
graph LR
    SIT_PRIMARY["dbsoravmctst21a:9091<br/>Primary SIT"]
    SIT_SECONDARY["dbsoravmctst21b:9092<br/>Secondary SIT"]

    SIT_PRIMARY <-.-> SIT_SECONDARY

    style SIT_PRIMARY fill:#fff3e0
    style SIT_SECONDARY fill:#fff3e0
```

#### Production Environment

```mermaid
graph LR
    PROD_PRIMARY["isssrvprd61a:8080<br/>Primary PROD"]
    PROD_SECONDARY["isssrvprd81a:8080<br/>Secondary PROD"]

    PROD_PRIMARY <-.-> PROD_SECONDARY

    style PROD_PRIMARY fill:#ffebee
    style PROD_SECONDARY fill:#ffebee
```

### Server Configuration Details

| Environment | Primary Server | Secondary Server | Port |
|-------------|----------------|------------------|------|
| **DEV** | dbsoravmctst21a | dbsoravmctst21b | 8081/8082 |
| **DEV (V2 Menu)** | dbsoravmctst21a | dbsoravmctst21b | 8083/8084 |
| **SIT** | dbsoravmctst21a | dbsoravmctst21b | 9091/9092 |
| **PROD** | isssrvprd61a | isssrvprd81a | 8080/8080 |

### Migration Plan

The following servers are scheduled for migration to new infrastructure:

```mermaid
graph LR
    subgraph "Current Production"
        CURRENT_1["isssrvprd61a<br/>Current PROD"]
        CURRENT_2["isssrvprd81a<br/>Current PROD"]
    end

    subgraph "New Production"
        MIGRATE_1["dbmoraprd61a<br/>New PROD Server"]
        MIGRATE_2["dbmoraprd81a<br/>New PROD Server"]
    end

    CURRENT_1 -.->|Migrate to| MIGRATE_1
    CURRENT_2 -.->|Migrate to| MIGRATE_2

    style CURRENT_1 fill:#ffebee
    style CURRENT_2 fill:#ffebee
    style MIGRATE_1 fill:#f3e5f5
    style MIGRATE_2 fill:#f3e5f5
```

:::info

DNS configuration needs to be applied for the new servers during migration.
:::

# V2 Menu Architecture

## Menu Flow

```mermaid
graph TB
    Client[Client Request] --> Controller[BaseMenuControllerV2]
    Controller --> Parser[Parse msgChain]
    Parser --> Validation[Validate Level]

    Validation --> Level1["Level 1:<br/>@itbot|SC3 Database Health Check"]
    Validation --> Level2["Level 2:<br/>...|{{SessionId}}"]
    Validation --> Level3["Level 3:<br/>...|{{Product}}|{{Command}}"]
    Validation --> LevelN["Level N:<br/>...|{{Additional Arguments}}"]

    Level1 --> Response[Menu Response]
    Level2 --> Response
    Level3 --> Response
    LevelN --> Response
```

The BaseMenuControllerV2 parses the msgChain by:

1. Splitting on "|" character
2. Validating the level based on pattern matching
3. Processing arguments using ICommandArgHandler implementations
4. Returning appropriate menu options for the next level


## Example Interaction

The V2 menu uses a message chain pattern to track user selections. Here's an example request:

```json
{
  "msgChain": "@itbot|SC3 Database Health Check|CLOUDERA|CLOUDERA_API_RESTART|ARG_CDP_CLUSTER",
  "requester": "John Doe",
  "corpID": "jd123",
  "chatopsEnv": "dev",
  "roomID": "12345",
  "token": "xxxxxxx"
}
```


Each level adds context to the http session, allowing stateful menu navigation.

### Chatroom Capture
![alt text](chatroom_capture_1.png)
![alt text](chatroom_capture_2.png)
![alt text](chatroom_capture_3.png)

### Sample log output with explanations

```json showLineNumbers=1
2025-08-08 16:18:36.554 DEBUG 45335 --- [nio-8083-exec-1] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:18:36.590 DEBUG 45335 --- [nio-8083-exec-1] h.o.h.s.s.c.s.JsonBodySessionIdResolver  : No session ID found in JSON body.
2025-08-08 16:18:36.591 DEBUG 45335 --- [nio-8083-exec-1] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/menu, client=**************]
2025-08-08 16:18:36.670 DEBUG 45335 --- [nio-8083-exec-1] h.o.h.s.s.service.TokenService           : Found chatroom with hashed token for room: 78856
2025-08-08 16:18:36.767 DEBUG 45335 --- [nio-8083-exec-1] h.o.h.s.s.c.s.JsonBodySessionIdResolver  : No session ID found in JSON body.
2025-08-08 16:18:36.767 DEBUG 45335 --- [nio-8083-exec-1] h.o.h.s.s.c.s.JsonBodySessionIdResolver  : Storing new/current session ID in request attribute: 23432a42-13ca-488c-8de0-3207926fb31f
2025-08-08 16:18:36.772 DEBUG 45335 --- [nio-8083-exec-1] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"msgChain":"@itbot|SC3 Database Health Check","requester":"Bon LAI","chatopsEnv":"dev","corpID":"lkp625","keyword":"{keyword}","roomID":"78856","token":"{{token}}"}]
2025-08-08 16:18:36.775 DEBUG 45335 --- [nio-8083-exec-1] h.o.h.s.s.c.s.JsonBodySessionIdResolver  : Storing new/current session ID in request attribute: 23432a42-13ca-488c-8de0-3207926fb31f
2025-08-08 16:21:30.568 DEBUG 45335 --- [nio-8083-exec-3] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:21:30.579 DEBUG 45335 --- [nio-8083-exec-3] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f]
2025-08-08 16:21:30.587 DEBUG 45335 --- [nio-8083-exec-3] h.o.h.s.s.service.TokenService           : Found chatroom with hashed token for room: 78856
2025-08-08 16:21:30.641 DEBUG 45335 --- [nio-8083-exec-3] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"msgChain":"@itbot|SC3 Database Health Check|23432a42-13ca-488c-8de0-3207926fb31f|CLOUDERA","requester":"Bon LAI","chatopsEnv":"dev","corpID":"lkp625","keyword":"{keyword}","roomID":"78856","token":"{{token}}"}]
2025-08-08 16:23:07.109 DEBUG 45335 --- [nio-8083-exec-5] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:23:07.114 DEBUG 45335 --- [nio-8083-exec-5] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f]
2025-08-08 16:23:07.121 DEBUG 45335 --- [nio-8083-exec-5] h.o.h.s.s.service.TokenService           : Found chatroom with hashed token for room: 78856
2025-08-08 16:23:07.203 DEBUG 45335 --- [nio-8083-exec-5] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"msgChain":"@itbot|SC3 Database Health Check|23432a42-13ca-488c-8de0-3207926fb31f|CLOUDERA|CLOUDERA_API_RESTART","requester":"Bon LAI","chatopsEnv":"dev","corpID":"lkp625","keyword":"{keyword}","roomID":"78856","token":"{{token}}"}]
2025-08-08 16:23:10.568 DEBUG 45335 --- [nio-8083-exec-6] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:23:10.571 DEBUG 45335 --- [nio-8083-exec-6] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f]
2025-08-08 16:23:10.575 DEBUG 45335 --- [nio-8083-exec-6] h.o.h.s.s.service.TokenService           : Found chatroom with hashed token for room: 78856
2025-08-08 16:23:10.605 DEBUG 45335 --- [nio-8083-exec-6] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"msgChain":"@itbot|SC3 Database Health Check|23432a42-13ca-488c-8de0-3207926fb31f|CLOUDERA|CLOUDERA_API_RESTART|ARG_CDP_CLUSTER","requester":"Bon LAI","chatopsEnv":"dev","corpID":"lkp625","keyword":"{\"ARG_CDP_CLUSTER\":\"sc3_sand01\"}","roomID":"78856","token":"{{token}}"}]
2025-08-08 16:23:12.988 DEBUG 45335 --- [nio-8083-exec-7] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:23:12.994 DEBUG 45335 --- [nio-8083-exec-7] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f]
2025-08-08 16:23:12.999 DEBUG 45335 --- [nio-8083-exec-7] h.o.h.s.s.service.TokenService           : Found chatroom with hashed token for room: 78856
2025-08-08 16:23:13.034 DEBUG 45335 --- [nio-8083-exec-7] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"msgChain":"@itbot|SC3 Database Health Check|23432a42-13ca-488c-8de0-3207926fb31f|CLOUDERA|CLOUDERA_API_RESTART|ARG_CDP_CLUSTER|ARG_CDP_SERVICE","requester":"Bon LAI","chatopsEnv":"dev","corpID":"lkp625","keyword":"{\"ARG_CDP_SERVICE\":\"HIVE_ON_TEZ\"}","roomID":"78856","token":"{{token}}"}]
2025-08-08 16:23:15.711 DEBUG 45335 --- [nio-8083-exec-8] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:23:15.713 DEBUG 45335 --- [nio-8083-exec-8] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f]
2025-08-08 16:23:15.718 DEBUG 45335 --- [nio-8083-exec-8] h.o.h.s.s.service.TokenService           : Found chatroom with hashed token for room: 78856
2025-08-08 16:23:15.738 DEBUG 45335 --- [nio-8083-exec-8] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/menu, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"msgChain":"@itbot|SC3 Database Health Check|23432a42-13ca-488c-8de0-3207926fb31f|CLOUDERA|CLOUDERA_API_RESTART|ARG_CDP_CLUSTER|ARG_CDP_SERVICE|ARG_CDP_HOST","requester":"Bon LAI","chatopsEnv":"dev","corpID":"lkp625","keyword":"{\"ARG_CDP_HOST\":\"bdvmc2c\",\"ARG_CDP_INSTANCE\":\"hive_on_tez-HIVESERVER2-32b7cc0f686999f6e9aee7e86d47eaf4\"}","roomID":"78856","token":"{{token}}"}]
2025-08-08 16:23:16.102 DEBUG 45335 --- [nio-8083-exec-9] h.o.h.s.s.service.TokenService           : Token validated as hashed token
2025-08-08 16:23:16.106 DEBUG 45335 --- [nio-8083-exec-9] o.s.w.f.CommonsRequestLoggingFilter      : Before request [POST /chatops/v2/health_check, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f]
2025-08-08 16:23:16.110  INFO 45335 --- [nio-8083-exec-9] h.o.h.s.s.c.BaseHealthCheckControllerV2  : Command CLOUDERA_API_RESTART. Variables {DB_TYPE=CLOUDERA, ROOM_ID=78856, CMD_GRP_ID=CLOUDERA_API_RESTART, ARG_CDP_HOST=bdvmc2c, ARG_CDP_CLUSTER=sc3_sand01, ARG_CDP_INSTANCE=hive_on_tez-HIVESERVER2-32b7cc0f686999f6e9aee7e86d47eaf4, TEAM=SC3, ARG_CDP_SERVICE=HIVE_ON_TEZ, REQUESTER_ID=lkp625}
2025-08-08 16:23:17.626  INFO 45335 --- [nio-8083-exec-9] h.o.h.s.s.service.ClouderaService        : Cloudera API command executed: restart
2025-08-08 16:23:17.697  INFO 45335 --- [nio-8083-exec-9] h.o.h.s.s.service.PDFService             : Coverting PDF ./pdf/250808T162316.pdf to Base64
2025-08-08 16:23:17.707 DEBUG 45335 --- [nio-8083-exec-9] o.s.w.f.CommonsRequestLoggingFilter      : After request [POST /chatops/v2/health_check, client=**************, session=23432a42-13ca-488c-8de0-3207926fb31f, payload={"requester":"Bon LAI","requestTime":"**********","msgChain":"@itbot|SC3 Database Health Check|23432a42-13ca-488c-8de0-3207926fb31f|CLOUDERA|CLOUDERA_API_RESTART|ARG_CDP_CLUSTER|ARG_CDP_SERVICE|ARG_CDP_HOST","chatopsEnv":"dev","corpID":"lkp625","keyword":"23432a42-13ca-488c-8de0-3207926fb31f","roomID":"78856","token":"{{token}}"}]
```


### Log explained at a glance

Use this phase view to scan quickly. Line numbers refer to the code block above (its own numbering):

| Phase | Lines | Summary | State change |
|------|-------|---------|--------------|
| 1) Session bootstrap + first menu | L1–L8 | Token OK, no session found, new session created, first menu request/response | sessionId created and attached |
| 2) Select product | L9–L12 | User selects product (CLOUDERA) | msgChain adds , CLOUDERA |
| 3) Select command | L13–L16 | User selects command (CLOUDERA_API_RESTART) | msgChain adds command |
| 4) Provide cluster | L17–L20 | Cluster argument provided | ARG_CDP_CLUSTER captured |
| 5) Provide service | L21–L24 | Service argument provided | ARG_CDP_SERVICE captured |
| 6) Provide host/instance | L25–L28 | Host + instance provided | ARG_CDP_HOST, ARG_CDP_INSTANCE captured |
| 7) Execute health check | L29–L34 | Health check POST, controller logs variables, Cloudera restart, PDF conversion, request completes | Command executed; response prepared |


#### Variables assembled (from controller log L31)
- DB_TYPE=CLOUDERA, TEAM=SC3
- ROOM_ID=78856, REQUESTER_ID=lkp625
- CMD_GRP_ID=CLOUDERA_API_RESTART
- ARG_CDP_CLUSTER=sc3_sand01
- ARG_CDP_SERVICE=HIVE_ON_TEZ
- ARG_CDP_HOST=bdvmc2c
- ARG_CDP_INSTANCE=hive_on_tez-HIVESERVER2-32b7cc0f686999f6e9aee7e86d47eaf4
