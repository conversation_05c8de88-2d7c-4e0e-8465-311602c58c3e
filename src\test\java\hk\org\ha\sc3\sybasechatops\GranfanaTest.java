package hk.org.ha.sc3.sybasechatops;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.service.GrafanaService;

@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class GranfanaTest {

	@Autowired
	private GrafanaService grafanaService;

	@Test
	void getDbInstanceStatusByProject() throws Exception {
		this.grafanaService.getDbInstanceStatusByProject();
	}

	@Test
	void getDbInstanceStatusOverview() throws Exception {
		this.grafanaService.getDbInstanceStatusOverview();
	}

	@Test
	void getOracleHostMon() throws Exception {
		this.grafanaService.getOracleHostMon("cmsoratst11a");
	}

	@Test
	void getOracleInstanceMon() throws Exception {
		this.grafanaService.getOracleInstanceMon("cmscr2a11", "cmsoratst11a");
	}

}
