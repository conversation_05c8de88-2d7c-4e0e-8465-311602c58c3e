---
sidebar_position: 1
---

# Introduction

This guide provides instructions for developers working on the SC3 Chatops development project.

# Getting Started

This guide will help you set up your development environment and get familiar with the SC3 Chatops project.

## Prerequisites

Before implementing new features, ensure you have:
- A development environment set up
- Access to the project repository
- Understanding of the project architecture
- Required dependencies installed

## Project Repositories

### Main Repositories
1. **[SC3 Chatops Repo](https://hagithub.home/IT-SUPPORT/sc3-chatops)** - Main application repository
2. **[SC3 Chatops API Testing Repo](https://hagithub.home/IT-SUPPORT/sc3-chatops-bruno-test)** - API testing suite
3. **[SC3 Internal Gitlab](https://hoitdbm02/)** - Internal GitLab instance

## Project Resources

### Project Management
- **[SC3 Automation Jira](https://hatool.home/jira/secure/RapidBoard.jspa?rapidView=2155&projectKey=DBOAUTOM)** - Project tracking and issue management

### Communication
- **[SC3 Automation Slack Channel](https://join.slack.com/t/sc3-automation/shared_invite/zt-3988amw6r-YWetD~2RdiDcODg~mNMTug)** - Team communication and updates


## Available Guides

### Getting Started
- **[Project Manual](./2_project-manual.md)** - Setup guide and project resources for new developers

### Architecture
- **[App Architecture](./3_Architecture/1_app_architecture.md)** - System architecture overview and component relationships
- **[Database](./3_Architecture/2_database.md)** - Database schema, configuration, and migration guidelines

### Deployment
- **[Java Deployment](./4_Deployment/1_java-deployment.md)** - Java application deployment procedures and best practices
- **[MyBatis Deployment](./4_Deployment/2_mybatis-deployment.md)** - MyBatis configuration and deployment guidelines

### Development Guides
- **[How to add a new arghandler](./5_Development/2_How-to-Implement-New-ArgHandler.md)** - Learn how to create and integrate new argument handlers
- **[How to add a new command service](./5_Development/1_How-to-Implement-Command-Service.md)** - Instructions for implementing new command services
- **[How to add a new command group button](./5_Development/3_How-to-Add-Command-Group-Button.md)** - Instructions for adding new command group buttons to the UI
- **[How to add a new chatroom](./5_Development/5_how-to-add-new-chatroom.md)** - Instructions for adding new chatroom functionality
- **[Remote Development](./5_Development/6_remote-development.md)** - Guide for remote development setup

### Project Management
- **[Revision History](./0_revision-history.md)** - Track changes and updates to this documentation
- **[Git Standards](./6_git-standards.md)** - Git workflow, commit conventions, and versioning guidelines
