package hk.org.ha.sc3.sybasechatops.controller.repserver;

import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.config.NotificationConfig;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdGrpTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseMenuController;
import hk.org.ha.sc3.sybasechatops.helper.ArrayUtils;
import hk.org.ha.sc3.sybasechatops.interfaces.IButtonView;
import hk.org.ha.sc3.sybasechatops.model.Approval;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheck;
import hk.org.ha.sc3.sybasechatops.model.SimpleMenu;
import hk.org.ha.sc3.sybasechatops.model.TextInput;
import hk.org.ha.sc3.sybasechatops.model.db.RepDbSub;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.repository.RepDbSubRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("repserver")
public class RepserverMenuController extends BaseMenuController {

        private RepDbSubRepository repDbSubRepository;
        private final TextInput repserverInstanceInput;

        public RepserverMenuController(DatabaseRepository databaseRepository,
                        CommandGroupRepository commandGroupRepository,
                        RepDbSubRepository repDbSubRepository,
                        NotificationConfig notificationConfig) {
                super(databaseRepository, commandGroupRepository, RepserverHealthCheckController.class,
                                notificationConfig);
                repserverInstanceInput = TextInput.builder()
                                .msg("Please input repserver instance name wildcast (e.g. %HA_PLVHC_RA3%)")
                                .store("{rep_instance}").nextLabel("@itbot|RepServer|Instance List").build();

                this.repDbSubRepository = repDbSubRepository;
        }

        @Override
        public DatabaseTypeEnum getType() {
                return DatabaseTypeEnum.REPSERVER;
        }

        @Override
        public ChatopsRespBase getBaseResponse(String msgChain, String token) {
                switch (msgChain) {
                        case "@itbot|RepServer":
                                return this.repserverInstanceInput;
                        default:
                                return super.getUnknownReply();
                }
        }

        @Override
        public ChatopsRespBase instanceSelectMenu(@RequestBody ChatopsReq requestBody) {
                log.debug("Enter repserver /instances");
                String msgChainsStr = requestBody.getMsgChain();
                String instanceWildcast = requestBody.getKeyword();

                String[] msgChains = msgChainsStr.split("\\|");
                int instanceListIndex = ArrayUtils.getIndex(msgChains, "Instance List");
                String[] subMsgChains = Arrays.copyOfRange(msgChains, instanceListIndex + 1, msgChains.length);
                int indexPos = (msgChains.length - 1) - instanceListIndex;

                String host;
                String instance;
                String commandGroupIdStr;
                String rds;
                String rdb;
                String freeText;
                String hcKeyword;
                HealthCheck repHealthCheck;
                String url;

                log.info("indexPos {}", indexPos);
                switch (indexPos) {
                        case 2:
                                return this.getCommandMenu(Collections.singletonList(CmdGrpTypeEnum.INSTANCE));
                        case 3:
                                host = subMsgChains[0];
                                instance = subMsgChains[1];
                                commandGroupIdStr = subMsgChains[2];

                                if (commandGroupIdStr.equals("DBSUB_Repdbs_MDROP")
                                                || commandGroupIdStr.equals("DBSUB_DROP") ||
                                                commandGroupIdStr.equals("FIND_DBSUB_REPDBS")) {

                                        String nextLabel = String.format("@itbot|RepServer|Instance List|%s|%s|%s|%s",
                                                        subMsgChains[0], subMsgChains[1],
                                                        subMsgChains[2], "Free Text");
                                        return TextInput.builder()
                                                        .msg(String.format("Please input argment for %s",
                                                                        commandGroupIdStr))
                                                        .store("{rep_instance}")
                                                        .nextLabel(nextLabel)
                                                        .build();
                                }

                                if (commandGroupIdStr.equals("RS_STARTUP")
                                                || commandGroupIdStr.equals("RS_STARTUP_STDALONE")) {
                                        return Approval.builder().keyword("approval")
                                                        .msg("Hi, APPROVERS. Please approve the request by REQUESTER.")
                                                        .build();
                                }

                                List<RepDbSub> repDbSublist = this.repDbSubRepository
                                                .findByIdHostAndIdInstance(subMsgChains[0], subMsgChains[1]);

                                List<IButtonView> buttonList = repDbSublist.stream()
                                                .map(element -> (IButtonView) element)
                                                .collect(Collectors.toList());
                                String[] dbSubOptions = buttonList.stream().map(item -> item.getButtonView())
                                                .toArray(String[]::new);

                                return SimpleMenu.builder().msg("Please select repserver db sub below")
                                                .options(dbSubOptions)
                                                .statusCode(200).build();
                        case 4:
                                host = subMsgChains[0];
                                instance = subMsgChains[1];
                                commandGroupIdStr = subMsgChains[2];
                                freeText = subMsgChains[3];
                                hcKeyword = String.format("%s:%s|%s@%s", host, instance,
                                        commandGroupIdStr, instanceWildcast);
                                if (commandGroupIdStr.equals("FIND_DBSUB_REPDBS")) {
                                        try {
                                                url = getHealthCheckUri(
                                                                WebMvcLinkBuilder.methodOn(
                                                                                RepserverHealthCheckController.class)
                                                                                .dbRepHealthCheckFreetext(null));
                                        } catch (IOException e) {
                                                log.error("Error parsing free text hc url", e);
                                                return ChatopsRespBase.builder().statusCode(500)
                                                                .msg("Error parsing free text url"
                                                                                + e.getMessage())
                                                                .build();
                                        }
                
                                        repHealthCheck = HealthCheck.builder().keyword(hcKeyword)
                                                        .msg("Rep Health check (free text) processing (KEYWORD)")
                                                        .url(url)
                                                        .statusCode(200)
                                                        .token(requestBody.getToken())
                                                        .build();
                                        return repHealthCheck;
                                }
                                return Approval.builder().keyword("approval")
                                        .msg("Hi, APPROVERS. Please approve the request by REQUESTER.")
                                        .build();

                        case 5:
                                commandGroupIdStr = subMsgChains[2];
                                log.info("commandGroupIdStr {}", commandGroupIdStr);

                                if (commandGroupIdStr.equals("RS_STARTUP") || commandGroupIdStr.equals("RS_STARTUP_STDALONE")) {
                                        return postApprovedHealthCheckAction(
                                                String.format("%s:%s|%s", subMsgChains[0], subMsgChains[1],
                                                subMsgChains[2]), subMsgChains[2], requestBody.getToken());
                                }

                                if (commandGroupIdStr.equals("FIND_DBSUB")
                                                || commandGroupIdStr.equals("SQM_SHOW")
                                                || commandGroupIdStr.equals("REP_HC")) {
                                        host = subMsgChains[0];
                                        instance = subMsgChains[1];
                                        commandGroupIdStr = subMsgChains[2];
                                        rds = subMsgChains[3];
                                        rdb = subMsgChains[4];
                                        String keyword = String.format("%s:%s|%s@%s@%s", host, instance,
                                                        commandGroupIdStr, rds,
                                                        rdb);

                                        try {
                                                url = getHealthCheckUri(
                                                                WebMvcLinkBuilder.methodOn(
                                                                                RepserverHealthCheckController.class)
                                                                                .dbRepHealthCheck(null));
                                        } catch (IOException e) {
                                                log.error("Error parsing circult breaker url", e);
                                                return ChatopsRespBase.builder().statusCode(500)
                                                                .msg("Error parsing circult breaker url"
                                                                                + e.getMessage())
                                                                .build();
                                        }

                                        repHealthCheck = HealthCheck.builder().keyword(keyword)
                                                        .msg("Rep Health check processing (KEYWORD)")
                                                        .url(url)
                                                        .statusCode(200)
                                                        .token(requestBody.getToken())
                                                        .build();
                                        return repHealthCheck;
                                }

                                if (commandGroupIdStr.equals("SQM_PURGE")) {

                                        String nextLabel = String.format(
                                                        "@itbot|RepServer|Instance List|%s|%s|%s|%s|%s|%s",
                                                        subMsgChains[0], subMsgChains[1],
                                                        subMsgChains[2], subMsgChains[3], subMsgChains[4], "Free Text");
                                        return TextInput.builder()
                                                        .msg(String.format("Please input argment for %s",
                                                                        commandGroupIdStr))
                                                        .store("{rep_instance}")
                                                        .nextLabel(nextLabel)
                                                        .build();
                                }
                                
                        case 6:
                                host = subMsgChains[0];
                                instance = subMsgChains[1];
                                commandGroupIdStr = subMsgChains[2];
                                freeText = subMsgChains[3];

                                hcKeyword = String.format("%s:%s|%s@%s", host, instance,
                                commandGroupIdStr, instanceWildcast);

                                if (commandGroupIdStr.equals("DBSUB_Repdbs_MDROP")
                                        || commandGroupIdStr.equals("DBSUB_DROP")) {
                
                                        try {
                                                url = getHealthCheckUri(
                                                                WebMvcLinkBuilder.methodOn(
                                                                                RepserverHealthCheckController.class)
                                                                                .dbRepHealthCheckFreetext(null));
                                                log.info(url);
                                        } catch (IOException e) {
                                                log.error("Error parsing free text hc url", e);
                                                return ChatopsRespBase.builder().statusCode(500)
                                                                .msg("Error parsing free text url"
                                                                                + e.getMessage())
                                                                .build();
                                        }
        
                                        repHealthCheck = HealthCheck.builder().keyword(hcKeyword)
                                                        .msg("Rep Health check (free text) processing (KEYWORD)")
                                                        .url(url)
                                                        .statusCode(200)
                                                        .token(requestBody.getToken())
                                                        .build();
                                        log.info(repHealthCheck.toString());
                                        return repHealthCheck;
                                }
                                return Approval.builder().keyword("approval")
                                        .msg("Hi, APPROVERS. Please approve the request by REQUESTER.")
                                        .build();

                        case 8: 
                                host = subMsgChains[0];
                                instance = subMsgChains[1];
                                commandGroupIdStr = subMsgChains[2];
                                rds = subMsgChains[3];
                                rdb = subMsgChains[4];

                                hcKeyword = String.format("%s:%s|%s@%s@%s@%s", host, instance,
                                                commandGroupIdStr, rds, rdb, instanceWildcast);

                                try {
                                        url = getHealthCheckUri(
                                                        WebMvcLinkBuilder.methodOn(
                                                                        RepserverHealthCheckController.class)
                                                                        .dbRepHealthCheckDbsubAndFreetext(null));
                                } catch (IOException e) {
                                        log.error("Error parsing dbsub free text hc url", e);
                                        return ChatopsRespBase.builder().statusCode(500)
                                                        .msg("Error parsing free text url"
                                                                        + e.getMessage())
                                                        .build();
                                }

                                repHealthCheck = HealthCheck.builder().keyword(hcKeyword)
                                                .msg("Rep Health check (dbsub + free text) processing (KEYWORD)")
                                                .url(url)
                                                .statusCode(200)
                                                .token(requestBody.getToken())
                                                .build();
                                return repHealthCheck;
                        default:
                                return instanceDisplayMenu(instanceWildcast, requestBody.getToken());
                }
        }

        private String getHealthCheckUri(Object method) throws IOException {
                URI uri;
                uri = WebMvcLinkBuilder.linkTo(method).toUri();
                return uri.toString();
        }

}
