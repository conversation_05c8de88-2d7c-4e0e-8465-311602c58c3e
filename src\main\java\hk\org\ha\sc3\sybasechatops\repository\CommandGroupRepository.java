package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.repository.Repository;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdGrpTypeEnum;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroup;

public interface CommandGroupRepository extends Repository<CommandGroup, String> {
    List<CommandGroup> findAll();

    /* find by enum dbtype */
    List<CommandGroup> findByDbType(DatabaseTypeEnum dbType);

    /* find by id */
    Optional<CommandGroup> findById(String id);

    /* find by enum dbtype and enabled */
    List<CommandGroup> findByDbTypeAndCmdTypeAndEnabledTrue(DatabaseTypeEnum dbType, CmdGrpTypeEnum cmdType);

    /* find by enum dbtype, list of cmdtypes, and enabled */
    List<CommandGroup> findByDbTypeAndCmdTypeInAndEnabledTrue(DatabaseTypeEnum dbType, List<CmdGrpTypeEnum> cmdTypes);

    List<CommandGroup> findByDbTypeAndEnabledTrue(DatabaseTypeEnum dbType);
}
