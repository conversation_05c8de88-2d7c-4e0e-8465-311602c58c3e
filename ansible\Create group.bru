meta {
  name: Create group
  type: http
  seq: 4
}

post {
  url: https://{{ansible_host}}/api/v2/inventories/{{inventory_id}}/groups/
  body: json
  auth: basic
}

params:query {
  ~id: 434
}

auth:basic {
  username: {{ansible_user}}
  password: {{ansible_password}}
}

body:json {
  {
      "name": "oracle",
      "description": "",
      "variables": ""
  }
}

vars:pre-request {
  inventory_id: 432
}
