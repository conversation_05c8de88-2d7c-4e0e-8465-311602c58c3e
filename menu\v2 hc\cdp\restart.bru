meta {
  name: restart
  type: http
  seq: 2
}

post {
  url: http://localhost:8080/chatops/v2/health_check
  body: json
  auth: none
}

body:json {
  {
    "requester": "Bon LAI",
    "requestTime": "**********",
    "msg<PERSON>hain": "@itbot|SC3 Database Health Check|{{session_id}}|CLOUDERA|CLOUDERA_API_RESTART|ARG_CDP_CLUSTER|ARG_CDP_SERVICE|ARG_CDP_HOST",
    "chatopsEnv": "dev",
    "corpID": "lkp625",
    "keyword": "b43c53df-a9b5-4235-8baa-67f9fd055c7c",
    "roomID": "78856",
    "token": "{{chatops_token}}"
  }
}
