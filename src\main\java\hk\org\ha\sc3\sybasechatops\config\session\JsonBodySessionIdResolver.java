package hk.org.ha.sc3.sybasechatops.config.session;

import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.session.web.http.HttpSessionIdResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
public class JsonBodySessionIdResolver implements HttpSessionIdResolver {

    private static final Logger logger = LoggerFactory.getLogger(JsonBodySessionIdResolver.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    public static final String SESSION_ID_REQUEST_ATTRIBUTE = "JsonBodySessionIdResolver.sessionId";

    @Override
    public List<String> resolveSessionIds(HttpServletRequest request) {

        try {
            ChatopsReq chatopsReq = objectMapper.readValue(request.getInputStream(), ChatopsReq.class);
            String[] msgChain = chatopsReq.getMsgChain().split("\\|");
            if (msgChain.length <= 2) {
                logger.debug("No session ID found in JSON body.");
                return Collections.emptyList();
            }
            String sessionId = msgChain[2];
            return Collections.singletonList(sessionId);
        } catch (IOException e) {
            logger.error("Failed to read session ID from JSON body", e);
        }

        return Collections.emptyList();
    }

    @Override
    public void setSessionId(HttpServletRequest request, HttpServletResponse response, String sessionId) {
        logger.debug("Storing new/current session ID in request attribute: {}", sessionId);
        request.setAttribute(SESSION_ID_REQUEST_ATTRIBUTE, sessionId);
    }

    @Override
    public void expireSession(HttpServletRequest request, HttpServletResponse response) {
        logger.debug("Expiring session ID (no specific action for JSON body transport).");
        request.removeAttribute(SESSION_ID_REQUEST_ATTRIBUTE);
    }
}