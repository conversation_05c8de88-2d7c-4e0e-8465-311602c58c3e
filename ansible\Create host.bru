meta {
  name: Create host
  type: http
  seq: 5
}

post {
  url: https://{{ansible_host}}/api/v2/groups/{{group_id}}/hosts/
  body: json
  auth: basic
}

params:query {
  ~id: 417
}

auth:basic {
  username: {{ansible_user}}
  password: {{ansible_password}}
}

body:json {
  {
      "name": "dbmoratst11f",
      "description": "",
      "enabled": true,
      "instance_id": "",
       "variables": "---\ninstances:\n  - sid: \"dbsreps12\""
  }
}

vars:pre-request {
  group_id: 579
}
