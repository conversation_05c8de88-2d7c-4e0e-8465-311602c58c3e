meta {
  name: Create inventory
  type: http
  seq: 3
}

post {
  url: https://{{ansible_host}}/api/v2/inventories/
  body: json
  auth: basic
}

params:query {
  ~id: 417
}

auth:basic {
  username: {{ansible_user}}
  password: {{ansible_password}}
}

body:json {
  {
      "name": "dbmoratst11f-dbsreps12-datapatch",
      "description": "",
      "organization": 39,
      "kind": "",
      "host_filter": null,
      "variables": "---",
      "prevent_instance_group_fallback": false
  }
  
}

vars:pre-request {
  org: 39
}
