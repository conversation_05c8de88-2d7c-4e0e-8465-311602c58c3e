---
sidebar_position: 5
---

# Git Standards

This document outlines the Git standards and conventions used in this project to ensure consistent, readable, and maintainable version control practices.

## Commit Message Format

We follow the [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) specification for all commit messages.

### Basic Format

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **build**: Changes that affect the build system or external dependencies
- **ci**: Changes to our CI configuration files and scripts
- **chore**: Other changes that don't modify src or test files
- **revert**: Reverts a previous commit

### Examples

```sh
# Feature addition
feat(auth): add OAuth2 authentication support

# Bug fix
fix(api): resolve null pointer exception in user service

# Documentation update
docs(readme): update installation instructions

# Breaking change
feat(api)!: change user authentication method

BREAKING CHANGE: The authenticate() method now requires a token parameter
```

## Semantic Versioning

We follow [Semantic Versioning](https://semver.org/) (SemVer) for all releases.

### Version Format

```
MAJOR.MINOR.PATCH
```

### Version Increment Rules

- **MAJOR**: Increment when you make incompatible API changes
- **MINOR**: Increment when you add functionality in a backwards compatible manner
- **PATCH**: Increment when you make backwards compatible bug fixes

### Pre-release Versions

```
1.0.0-alpha.1
1.0.0-beta.2
1.0.0-rc.1
```

### Examples

```bash
# Initial release
1.0.0

# Bug fix
1.0.1

# New feature (backwards compatible)
1.1.0

# Breaking change
2.0.0

# Pre-release
2.0.0-alpha.1
```

## Branch Naming Conventions

### Main Branches

- **main**: Production-ready code
- **develop**: Integration branch for features

### Feature Branches Naming
Branch name should be the current version that the feature is targeting + JIRA issue number (e.g. v2.2.0-62)
v2.2.0-62

## Git Workflow

### 1. Feature Development

```bash
# Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b v2.2.0-62

# Make changes and commit
git add .
git commit -m "feat(component): add new feature implementation"

# Push and create pull request
git push origin v2.2.0-62
```

### 2. Hotfix Process

```bash
# Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b v2.2.0-88

# Make changes and commit
git add .
git commit -m "fix(security): patch authentication vulnerability"

# Push and create pull request to main
git push origin v2.2.0-88
```

## Pull Request Guidelines

### PR Title Format

Follow the same format as commit messages:

```
feat(auth): implement OAuth2 authentication (DBOAUTOM-16)
fix(api): resolve memory leak in data processing (DBOAUTOM-26)
docs(readme): update API documentation (DBOAUTOM-23)
```

## Release Process

```bash
# Update version numbers in pom.xml and commit
git add .
git commit -m "release: v2.2.0"

# Create and push tag for the release
git tag -a v2.2.0 -m "Release version 2.2.0"
git push origin v2.2.0
```
Create release in github https://hagithub.home/IT-SUPPORT/sc3-chatops/releases/new

## Best Practices

### Commit Guidelines

1. **Use imperative mood**: "Add feature" not "Added feature"
2. **Keep first line under 50 characters**
3. **Use body to explain what and why, not how**
4. **Reference issues in PR title**
5. **Use breaking change footer for major changes**

### Example Bad Commits

```bash
# Too vague
fix: bug fix

# Not descriptive
update stuff

# Wrong tense
added new feature

# Too long first line
feat(auth): implement comprehensive OAuth2 authentication system with support for multiple providers
```

## References

- [Conventional Commits Specification](https://www.conventionalcommits.org/en/v1.0.0/)
- [Semantic Versioning Specification](https://semver.org/)
- [Git Flow Workflow](https://nvie.com/posts/a-successful-git-branching-model/)
- [GitHub Flow](https://guides.github.com/introduction/flow/)