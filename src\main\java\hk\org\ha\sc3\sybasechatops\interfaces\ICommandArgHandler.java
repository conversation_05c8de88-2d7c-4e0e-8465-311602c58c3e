package hk.org.ha.sc3.sybasechatops.interfaces;

import java.util.List;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.model.AdancedMenu;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;

import javax.servlet.http.HttpSession;

public abstract class ICommandArgHandler {

    public abstract CmdArgEnum getCmdArgType();

    /**
     * Retrieves options for the command argument.
     * 
     * @param argument          The command argument.
     * @param storeVariablesMap The map of stored variables from the session.
     * @param msgChain          The message chain.
     * @return A list of options.
     */
    public abstract List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain);

    public ChatopsRespBase getChatopsResp(CommandArgument argument, HttpSession httpSession, String msgChain) {

        List<Option> options = getOptions(argument, httpSession, msgChain);

        AdancedMenu menuResponse = AdancedMenu.builder().msg(argument.getDescription()).options(options).statusCode(200)
                .build();
        return menuResponse;
    }
}
