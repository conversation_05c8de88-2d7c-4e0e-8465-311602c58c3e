meta {
  name: orarole command
  type: http
  seq: 2
}

post {
  url: http://localhost:8080/chatops/menu/oracle/health_check
  body: json
  auth: none
}

body:json {
  {
    "requester": "Bon LAI",
    "requestTime": "**********",
    "msg<PERSON>hain": "@itbot|Oracle|Host Search|Host List|dbmoratst11c|ORAROLE|dbsreps11",
    "corpID": "lkp625",
    "keyword": "dbmoratst11c:dbsreps11|ORAROLE",
    "chatopsEnv": "{{env}}",
    "roomID": "{{room_id}}",
    "token": "{{chatops_token}}"
  }
}
