package hk.org.ha.sc3.sybasechatops.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "grafana")
public class GrafanaConfig {
    private String baseUrl;
    private String username;
    private String password;
    private long screenshotDelayMs;
    private int waitTimeoutSecond;
    private String serverDomain;
}