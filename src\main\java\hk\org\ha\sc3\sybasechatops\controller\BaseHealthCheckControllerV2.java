package hk.org.ha.sc3.sybasechatops.controller;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.SessionAttrEnum;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckKeywordItem;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckReturn;
import hk.org.ha.sc3.sybasechatops.model.PdfContent;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.CommandGroupService;
import hk.org.ha.sc3.sybasechatops.service.PDFService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("v2/health_check")
public class BaseHealthCheckControllerV2 extends BaseHealthCheckController {

        public BaseHealthCheckControllerV2(PDFService pdfService, DatabaseRepository databaseRepository,
                        CommandGroupService commandGroupService) {
                super(pdfService, databaseRepository, commandGroupService);
        }

        @Override
        @PostMapping
        public ChatopsRespBase dbHealthCheck(@RequestBody ChatopsReq requestBody, HttpSession httpSession) {
                HashMap<String, String> sessionMap = new HashMap<>();
                Collections.list(httpSession.getAttributeNames())
                                .forEach(name -> sessionMap.put(name, (String) httpSession.getAttribute(name)));

                HealthCheckKeywordItem item = HealthCheckKeywordItem.builder()
                                .commandGroupId(sessionMap.get(SessionAttrEnum.CMD_GRP_ID.name()))
                                .storeVariablesMap(sessionMap).build();
                HealthCheckReturn hcReturn;
                log.debug("Enter {} health check", getType());
                // for (HealthCheckKeywordItem item : healthCheckKeyword.getItems()) {

                String requester = requestBody.getRequester();
                Instant instant = Instant.ofEpochSecond(requestBody.getRequestTime());
                LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Hong_Kong"));

                List<PdfContent> pdfContents = new ArrayList<>();

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd'T'HHmmss");
                String fileName = String.format("%s.pdf", dateTime.format(formatter));

                log.info("Command {}. Variables {}", item.getCommandGroupId(), item.getStoreVariablesMap());

                long hcStartTime = System.currentTimeMillis();
                log.debug("Start timestamp: {}", hcStartTime);

                List<CommandResult> healthCheckContent;

                try {
                        healthCheckContent = getHealthCheckContent("null", "null", item.getCommandGroupId(),
                                        item.getStoreVariablesMap());
                        // Mark elapse time
                        long hcEndTime = System.currentTimeMillis();
                        log.debug("End timestamp: {}", hcEndTime);
                        double hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
                        PdfContent pdfContent = PdfContent.builder().hostName("null").instanceName("null")
                                        .healthCheckContent(healthCheckContent).hcElapsedTime(hcElapsedTime).build();
                        pdfContents.add(pdfContent);

                        pdfService.generateDbHealthCheckPdf(fileName, requester, dateTime, pdfContents, sessionMap);
                        String base64;
                        base64 = this.pdfService.generateBase64FromPdf(fileName);
                        log.debug("filename {}", fileName);
                        log.debug("base64 {}", base64);

                        hcReturn = HealthCheckReturn.builder().statusCode(200).file_output_name(fileName)
                                        .msg(String.format("Here is the health check report (%s)", httpSession.getId()))
                                        .pdfResult(base64).store(null).build();

                        return hcReturn;
                } catch (IOException e) {
                        log.error("Error in generating PDF {}", fileName, e);
                        hcReturn = HealthCheckReturn.builder().statusCode(500)
                                        .msg("Error in generating PDF").store(null).build();
                        return hcReturn;
                } catch (Exception e) {
                        log.error("Error in executing health check v2", e);
                        hcReturn = HealthCheckReturn.builder().statusCode(500)
                                        .msg("Error in executing health check v2").store(null).build();
                        return hcReturn;
                }
                // }
        }

        @Override
        public DatabaseTypeEnum getType() {
                return null;
        }
}
