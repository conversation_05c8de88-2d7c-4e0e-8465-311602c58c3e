package hk.org.ha.sc3.sybasechatops.config;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.jar.*;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "folderdistributor")
public class FolderDistributorConfig {
    private String jarPath;
    private String oemDestinationFolderPath;
    private String startDestinationFolderPath;

    @Bean
    public void ScriptDistribute() {
        String oemSourceFolderPath = "BOOT-INF/classes/script/oem";
        String startSourceFolderPath = "BOOT-INF/classes/script/start";
        String jarPath = this.jarPath;
        String oemDestinationFolderPath = this.oemDestinationFolderPath;
        String startDestinationFolderPath = this.startDestinationFolderPath;
        File jarFile = new File(jarPath);
        try (JarFile jar = new JarFile(jarFile)) {
            Enumeration<JarEntry> entries = jar.entries();
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                if (entry.getName().startsWith(oemSourceFolderPath) && !entry.isDirectory()) {
                    String relativePath = entry.getName().substring(oemSourceFolderPath.length());
                    Path destPath = Paths.get(oemDestinationFolderPath, relativePath);
                    // Files.createDirectories(destPath.getParent()); // Create parent directories
                    try (InputStream is = jar.getInputStream(entry)) {
                        Files.copy(is, destPath, StandardCopyOption.REPLACE_EXISTING);
                    }
                }
                if (entry.getName().startsWith(startSourceFolderPath) && !entry.isDirectory()) {
                    String relativePath = entry.getName().substring(startSourceFolderPath.length());
                    Path destPath = Paths.get(startDestinationFolderPath, relativePath);
                    // Files.createDirectories(destPath.getParent()); // Create parent directories
                    try (InputStream is = jar.getInputStream(entry)) {
                        Files.copy(is, destPath, StandardCopyOption.REPLACE_EXISTING);
                    }
                }
            }
            log.info("Script Distribution Success! [ {}, {} ]", oemDestinationFolderPath, startDestinationFolderPath);
        } catch (IOException e) {
            log.info("Script Distribution Failed! [ {} ]", "File/Folder Not Found!");
            e.printStackTrace();
        }
    }
}
