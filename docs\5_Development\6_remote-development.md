# Remote development w/ vscode

## [Syncthing](https://github.com/syncthing/syncthing)

We use syncthing to sync program code between source PC and remote target. It is a continuous file synchronization program that synchronizes files between two or more computers in real time, safely protected from prying eyes.

### Source PC (Windows)

1. Download and install [syncthingtray](https://martchus.github.io/syncthingtray/#downloads-section)

2. Generate SSH key pair if not already done:
```cmd
ssh-keygen -t rsa -b 4096
```

Output:
```
Generating public/private rsa key pair.
Enter file in which to save the key (C:\Users\<USER>\Users\${corp_id}\.ssh`

```sh
Host sshgateway
	HostName gwyvmc0b
	User ${corp_id}
	Port 22
	IdentityFile ${C:/Users/<USER>/.ssh/id_rsa}

Host dbsoravmctst21a
	HostName dbsoravmctst21a
	User oracle
	MACs hmac-sha2-256
	Ciphers aes256-ctr
	ProxyJump sshgateway
	IdentityFile ${C:/Users/<USER>/.ssh/id_rsa}
```

4. Copy public key from source PC to both sshgateway and remote target

### Remote Target (Linux)

The below setup is for the remote target, which is a Linux server where the development will take place.
Only one initialization is needed.
1. Prerequisites: miniconda installed in the remote target.
Download [syncthing](https://anaconda.org/conda-forge/syncthing/files) and install.
```bash
conda install syncthing-1.29.7-ha8f183a_0.conda
```

2. Edit config to disable relay listener. Set `<relaysEnabled>false</relaysEnabled>`
```sh
vi /dbms/maintain/home/<USER>/.local/state/syncthing/config.xml
```

3. Start syncthing as background process
```sh
syncthing serve --gui-address=0.0.0.0:50000 &
```

:::info

If syncthing is not running, start it if needed:
```sh
# Check if syncthing process is running
ps aux | grep syncthing

# If syncthing process does not exist, run the syncthing serve command again
syncthing serve --gui-address=0.0.0.0:50000 &
```

:::

Finally, set up connection between syncthing hosts (To be completed).

## maven

### Source PC (Windows)
1. Run `maven package -DskipTests` command in source PC, in order to download all libraries jar to local repository directory. \
Then, sync the directory `C:/Users/<USER>/.m2/repo` to remote target (`/dbms/oratmp/.m2/repository` in this case) using scp.

### Remote Target (Linux)
1. Install [openjdk](https://adoptium.net/temurin/releases/?version=8&os=any&arch=any) if not already installed.

2. Using offline installation method, install maven on remote target if not already installed.
Please refer to https://maven.apache.org/install.html.

3. Edit mvn setting file at `$mvn_binary}/apache-maven-3.9.10/conf/settings.xml` to use local repository.
```xml
    <offline>true</offline>
    <localRepository>/dbms/oratmp/.m2/repository</localRepository>
```

4. Ensure maven binary & java is exported in `~/.bashrc`.
```sh
export PATH=$PATH:/dbms/oratmp/apache-maven-3.9.10/bin:/dbms/oratmp/jdk8u452-b09/bin
export JAVA_HOME=/dbms/oratmp/jdk8u452-b09
```

## vscode

### Source PC (Windows)
1. Edit source PC user setting `settings.json`
```json
  "remote.SSH.remotePlatform": {
    "dbsoravmctst21a": "linux"
  },
  "remote.SSH.serverInstallPath": {
    "dbsoravmctst21a": "/dbms/oratmp/.vscode-server/"
  }
```

### Remote Target (Linux)
1. In remote VS code session, open `Open Remote Setting (JSON)` option. Add remote vscode setting
```json
{
  "java.configuration.maven.userSettings": "/dbms/oratmp/apache-maven-3.9.10/conf/settings.xml",
  "maven.executable.path": "/dbms/oratmp/apache-maven-3.9.10/bin/mvn"
}
```

## References

- https://code.visualstudio.com/docs/remote/ssh