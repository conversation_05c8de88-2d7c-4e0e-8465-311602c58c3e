-- Description: Add CommandArgument and relationship to CommandGroup

-- Create command argument table
CREATE TABLE `health_check`.`chatops_command_argument` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `key_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `arg_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create join table for many-to-many relationship
CREATE TABLE `health_check`.`chatops_command_argument_mapping` (
  `cmd_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `argument_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sequence` int NOT NULL,
  `required` int NOT NULL,
  `enabled` int NOT NULL,
  PRIMARY KEY (`cmd_id`,`argument_id`),
  KEY `FK_argument_id` (`argument_id`),
  CONSTRAINT `FK_cmd_id` FOREIGN KEY (`cmd_id`) REFERENCES `health_check`.`chatops_command` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_argument_id` FOREIGN KEY (`argument_id`) REFERENCES `health_check`.`chatops_command_argument` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create command argument table
CREATE TABLE `health_check`.`chatops_user_session` (
  `sessionId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sessionPayload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `requester_id` VARCHAR(255),
  `room_id` VARCHAR(255),
  `created_datetime` TIMESTAMP,
  `last_modify_datetime` TIMESTAMP,
  PRIMARY KEY (`sessionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE CHATOP_SPRING_SESSION (
	PRIMARY_ID CHAR(36) NOT NULL,
	SESSION_ID CHAR(36) NOT NULL,
	CREATION_TIME BIGINT NOT NULL,
	LAST_ACCESS_TIME BIGINT NOT NULL,
	MAX_INACTIVE_INTERVAL INT NOT NULL,
	EXPIRY_TIME BIGINT NOT NULL,
	PRINCIPAL_NAME VARCHAR(100),
	CONSTRAINT CHATOP_SPRING_SESSION_PK PRIMARY KEY (PRIMARY_ID)
) ENGINE=InnoDB ROW_FORMAT=DYNAMIC;

CREATE UNIQUE INDEX CHATOP_SPRING_SESSION_IX1 ON CHATOP_SPRING_SESSION (SESSION_ID);
CREATE INDEX CHATOP_SPRING_SESSION_IX2 ON CHATOP_SPRING_SESSION (EXPIRY_TIME);
CREATE INDEX CHATOP_SPRING_SESSION_IX3 ON CHATOP_SPRING_SESSION (PRINCIPAL_NAME);

CREATE TABLE CHATOP_SPRING_SESSION_ATTRIBUTES (
	SESSION_PRIMARY_ID CHAR(36) NOT NULL,
	ATTRIBUTE_NAME VARCHAR(200) NOT NULL,
	ATTRIBUTE_BYTES BLOB NOT NULL,
	CONSTRAINT CHATOP_SPRING_SESSION_ATTRIBUTES_PK PRIMARY KEY (SESSION_PRIMARY_ID, ATTRIBUTE_NAME),
	CONSTRAINT CHATOP_SPRING_SESSION_ATTRIBUTES_FK FOREIGN KEY (SESSION_PRIMARY_ID) REFERENCES CHATOP_SPRING_SESSION(PRIMARY_ID) ON DELETE CASCADE
) ENGINE=InnoDB ROW_FORMAT=DYNAMIC;


INSERT INTO health_check.chatops_command_argument (`id`, `key_name`, `description`, `arg_type`) VALUES
  ('1','cluster','Please select the cluster','CLOUDERA_CLUSTER'),
  ('2','service','Please select the service','CLOUDERA_SERVICE');
  ('3','host','Please select the host','HOST')

INSERT INTO health_check.chatops_command_argument_mapping (`cmd_id`, `argument_id`, `sequence`, `required`, `enabled`) VALUES
  ('CLOUDERA_API_CHK_ROLE', '1', 1, 1, 1),
  ('CLOUDERA_API_CHK_ROLE', '2', 2, 1, 1),
  ('ORAROLE', '3', 1, 1, 1);


CREATE TABLE `health_check`.`chatops_global_allowed_db` (
  `database_type` varchar(50) NOT NULL,
  PRIMARY KEY (`database_type`)
);

-- Add isApproval column to chatops_command_group table
-- This migration adds support for approval workflow functionality

ALTER TABLE `chatops_command_group` 
ADD COLUMN `is_approval` TINYINT(1) NOT NULL DEFAULT 0 
COMMENT 'Indicates if this command group requires approval before execution';

-- //@UNDO
DROP TABLE `health_check`.`chatops_command_argument_mapping`;
DROP TABLE `health_check`.`chatops_command_argument`;
DROP TABLE `health_check`.`chatops_user_session`;

DROP TABLE `health_check`.`chatops_global_allowed_db`;

ALTER TABLE `chatops_command_group` 
DROP COLUMN `is_approval`;