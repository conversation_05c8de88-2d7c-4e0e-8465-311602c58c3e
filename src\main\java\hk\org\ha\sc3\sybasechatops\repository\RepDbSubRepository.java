package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;

import org.springframework.data.repository.CrudRepository;

import hk.org.ha.sc3.sybasechatops.model.db.RepDbSub;
import hk.org.ha.sc3.sybasechatops.model.db.id.RepDbSubId;

public interface RepDbSubRepository extends CrudRepository<RepDbSub, RepDbSubId> {

    List<RepDbSub> findByIdHostAndIdInstance(String host, String instance);

}
