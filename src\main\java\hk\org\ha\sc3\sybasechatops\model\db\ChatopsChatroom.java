package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
public class ChatopsChatroom implements Serializable {
    @Id
    private String roomId;

    @Column(nullable = false, unique = true)
    private String token;
    private String roomName;

    @Column(nullable = false)
    private String team;

}
