package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;

import org.springframework.data.repository.CrudRepository;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.model.db.GlobalAllowedDB;

public interface GlobalAllowedDBRepository extends CrudRepository<GlobalAllowedDB, DatabaseTypeEnum> {
    /* Find all global allowed database entities */
    List<GlobalAllowedDB> findAll();
}