package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.repository.Repository;

import hk.org.ha.sc3.sybasechatops.model.db.ClouderaCluster;

public interface ClouderaClusterRepository extends Repository<ClouderaCluster, Integer> {
    List<ClouderaCluster> findAll();

    Optional<ClouderaCluster> findByClusterName(String clusterName);
}
