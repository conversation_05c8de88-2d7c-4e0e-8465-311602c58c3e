package hk.org.ha.sc3.sybasechatops.component.arghandler.cdp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaComponentRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CdpServiceArg<PERSON>and<PERSON> extends ICommandArgHandler {

    private ClouderaComponentRepository clouderaComponentRepository;

    public CdpServiceArgHandler(ClouderaComponentRepository clouderaComponentRepository) {
        this.clouderaComponentRepository = clouderaComponentRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_CDP_SERVICE;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        String cluster = (String) httpSession.getAttribute(CmdArgEnum.ARG_CDP_CLUSTER.name());

        if (cluster != null) {
            clouderaComponentRepository.findDistinctRoleByCluster(cluster).forEach(service -> {
                hiddenValue.put(getCmdArgType().name(), service);
                try {
                    String jsonStr = mapper.writeValueAsString(hiddenValue);
                    Option option = Option.builder()
                        .text(service)
                        .value(jsonStr)
                        .nextLabel(msgChain + "|" + getCmdArgType())
                        .build();
                    options.add(option);
                } catch (JsonProcessingException e) {
                    log.error("Error processing JSON for service: {}", service, e);
                }
            });
        }
        return options;
    }
}
