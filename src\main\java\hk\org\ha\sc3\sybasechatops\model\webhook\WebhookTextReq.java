package hk.org.ha.sc3.sybasechatops.model.webhook;

import hk.org.ha.sc3.sybasechatops.constant.webhook.WebhookTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class WebhookTextReq extends WebhookReqBase {
    private String text;

    @Builder
    public WebhookTextReq(String roomId, String text) {
        super(WebhookTypeEnum.text, roomId);
        this.text = text;
    }
    
    // Additional constructor to match the 1-argument call pattern
    public WebhookTextReq(String text) {
        super(null); // Use null or a default room ID
        this.text = text;
    }
}
