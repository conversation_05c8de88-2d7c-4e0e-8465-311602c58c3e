package hk.org.ha.sc3.sybasechatops;

import java.util.List;

import javax.transaction.Transactional;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import lombok.extern.slf4j.Slf4j;

/* A test class for testing DatabaseRepository methods */
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
@Slf4j
public class DatabaseRepositoryTests {

    @Autowired
    private DatabaseRepository databaseRepository;

    @Test
    @Transactional
    public void testInstanceFinds() {
        List<Database> databases = this.databaseRepository.findAll();
        // Function  functions = databases.get(1500).getDbFunctions().get(0).getFunction();
        // log.info("funcitons: {}",functions.getAbbr());
        log.info("databases: {}", databases);
        // verify
        // assertThat(retrievedEntity).isEqualTo(savedEntity);
    }

    @Test
    public void testFindFuncCode() {
        List<String>
        function=this.databaseRepository.findDistinctProjectByTypeAndProjLike(DatabaseTypeEnum.ORACLE,"D%");

        log.info("databases: {}", function);

    }

    @Test
    public void testFindHosts() {
        List<String>
        function=this.databaseRepository.findHostsByTypeAndProj(DatabaseTypeEnum.ORACLE,"OEM");

        log.info("databases: {}", function);

    }
    
    @Test
    public void testFindDatabase() {
        List<Database>
        databases=this.databaseRepository.findByTypeAndProj(DatabaseTypeEnum.ORACLE,"OEM");

        log.info("databases: {}", databases);

    }
}
