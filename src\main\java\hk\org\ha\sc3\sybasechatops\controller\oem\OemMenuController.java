package hk.org.ha.sc3.sybasechatops.controller.oem;

import java.io.IOException;
import java.util.EnumSet;
import java.util.concurrent.TimeUnit;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.FileReader;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.channel.ClientChannel;
import org.apache.sshd.client.channel.ClientChannelEvent;
import org.apache.sshd.client.keyverifier.AcceptAllServerKeyVerifier;
import org.apache.sshd.client.session.ClientSession;

import hk.org.ha.sc3.sybasechatops.config.OemConfig;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.helper.ArrayUtils;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.Reply;
import hk.org.ha.sc3.sybasechatops.model.SimpleMenu;
import hk.org.ha.sc3.sybasechatops.model.TextInput;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.OsPasswordService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("oma")
public class OemMenuController {

        private final TextInput omaInput;
        private OsPasswordService osPasswordService;
        private SshClient client;
        private OemConfig oemConfig;
        private final Reply unknownReply;

        public OemMenuController(DatabaseRepository databaseRepository, OsPasswordService osPasswordService, CommandGroupRepository commandGroupRepository, OemConfig oemConfig) {
                omaInput = TextInput.builder()
                                .msg("Please input OEM Agent name (e.g. mql014vmcprd61a)")
                                .store("{oma_host}").nextLabel("@itbot|OMA agent|OMA Search").build();

                this.osPasswordService = osPasswordService;
                client = SshClient.setUpDefaultClient();
                client.start();
                this.oemConfig = oemConfig;
                unknownReply = Reply.builder().msg("Unknown option").statusCode(200).build();
        }

        public DatabaseTypeEnum getType() {
                return DatabaseTypeEnum.OMA;
        }

        @PostMapping
        public ChatopsRespBase baseMenu(@RequestBody ChatopsReq requestBody) {
                return getBaseResponse(requestBody.getMsgChain());
        }

        public ChatopsRespBase getBaseResponse(String msgChain) {
                switch (msgChain) {
                        case "@itbot|OMA agent":
                                return this.omaInput;
                        default:
                                return unknownReply;
                }
        }

        @PostMapping("hosts")
        public ChatopsRespBase hostSelectMenu(@RequestBody ChatopsReq requestBody) {
                log.debug("Enter /hosts");
                String msgChainsStr = requestBody.getMsgChain();
                String hostWildcast = requestBody.getKeyword();

                String[] msgChains = msgChainsStr.split("\\|");
                int hostListIndex = ArrayUtils.getIndex(msgChains, "OMA Search");
                int indexPos = (msgChains.length - 1) - hostListIndex;

                switch (indexPos) {
                        default:
                                StringBuilder agentResult = new StringBuilder();
                                OMACheckStatus(hostWildcast);
                                agentResult = OMAGetStatus();
                                String agentStatus = agentResult.toString();
                                String [] result = new String[] {};
                                return SimpleMenu.builder().msg(agentStatus).options(result)
                                .statusCode(200).build();
                }       
        }

        public StringBuilder OMACheckStatus(String hostWildcast) {
                try (SshClient client = SshClient.setUpDefaultClient()) {
                        client.setServerKeyVerifier(AcceptAllServerKeyVerifier.INSTANCE);
                        client.start();

                        String hostname = oemConfig.getScriptHost();
                        int port = 22;
                        String username = oemConfig.getScriptHostUser();
                        String scriptPath = oemConfig.getShMasterDirectory() + "oem.sh";
                        String command = "bash " + scriptPath + " " + hostWildcast + " " + oemConfig.getYmlPath();
                        try (ClientSession session = client.connect(username, hostname, port).verify().getSession()) {
                                session.addPasswordIdentity(this.osPasswordService.getPassword(username, hostname));
                                session.auth().verify();

                                try (ClientChannel channel = session.createExecChannel(command)) {
                                        ByteArrayOutputStream output = new ByteArrayOutputStream();
                                        channel.setOut(output);
                                        channel.open().verify();
                                        channel.waitFor(EnumSet.of(ClientChannelEvent.CLOSED), TimeUnit.SECONDS.toMillis(30));
                                        session.close(false);
                                }
                        } catch (IOException e) {
                                log.info("exception", e);
                        }
                        } catch (Exception e) {
                                log.info("exception", e);
                        }
                StringBuilder result = new StringBuilder();
                result.append("Failed to reach the agent.");
                return result;
        }

        public StringBuilder OMAGetStatus() {
                try (SshClient client = SshClient.setUpDefaultClient()) {
                        client.setServerKeyVerifier(AcceptAllServerKeyVerifier.INSTANCE);
                        client.start();

                        String hostname = oemConfig.getScriptHost();
                        int port = 22;
                        String username = oemConfig.getScriptHostUser();
                        String filePath = oemConfig.getShMasterDirectory() + "result.txt";
                        String command = "rm " + filePath;

                        try (ClientSession session = client.connect(username, hostname, port).verify().getSession()) {
                                session.addPasswordIdentity(this.osPasswordService.getPassword(username, hostname));
                                session.auth().verify();

                                try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
                                                        
                                        StringBuilder content = new StringBuilder();
                                        String line;
        
                                        while ((line = reader.readLine()) != null) {
                                                content.append(line).append("\n");
                                        }
                                        
                                        try (ClientChannel channel = session.createExecChannel(command)) {
                                                ByteArrayOutputStream output = new ByteArrayOutputStream();
                                                channel.setOut(output);
                                                channel.open().verify();
                                        } catch (IOException e) {
                                                log.info("exception", e);
                                        }
                                        
                                        session.close(false);
                                        return new StringBuilder(content.toString());
                                }
                        } catch (IOException e) {
                                log.info("exception", e);
                        }
                        } catch (Exception e) {
                                log.info("exception", e);
                        }
                StringBuilder result = new StringBuilder();
                result.append("Failed to reach the agent");
                return result;
        }            
}
