package hk.org.ha.sc3.sybasechatops.service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import hk.org.ha.sc3.sybasechatops.config.WebhookConfig;
import hk.org.ha.sc3.sybasechatops.model.PdfContent;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.webhook.WebhookPdfReq;
import hk.org.ha.sc3.sybasechatops.model.webhook.WebhookResp;
import hk.org.ha.sc3.sybasechatops.model.webhook.WebhookTextReq;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Service
public class WebhookService {
    private WebhookConfig webhookConfig;
    private HttpClient sslHttpClient;
    private PDFService pdfService;

    public WebhookService(WebhookConfig webhookConfig, HttpClient sslHttpClient, PDFService pdfService) {
        this.webhookConfig = webhookConfig;
        this.sslHttpClient = sslHttpClient;
        this.pdfService = pdfService;
    }

    public Mono<WebhookResp> sendHealthCheckPdf(String fileName, LocalDateTime dateTime, String msg,
            CommandResult result) {
        String roomId = result.getCommandObj().getStoreVariables().get("roomId");
        PdfContent pdfContent = PdfContent.builder().hostName(result.getCommandObj().getHost())
                .instanceName(result.getCommandObj().getInstance())
                .healthCheckContent(Collections.singletonList(result)).hcElapsedTime(result.getElapsedTime()).build();

        this.pdfService.generateDbHealthCheckPdf(fileName, result.getCommandObj().getStoreVariables().get("requester"),
                dateTime, Collections.singletonList(pdfContent));

        Optional<String> base64 = Optional.empty();

        try {
            base64 = Optional.of(this.pdfService.generateBase64FromPdf(fileName));
        } catch (IOException e) {
            return this.replyTextMono(roomId, "Error when generating the Ansible webhook PDF");
        }

        return this.replyPdfMono(roomId, fileName, msg, base64.get());
    }

    public Mono<WebhookResp> replyTextMono(String roomId, String text) {
        WebhookTextReq webhookTextReq = WebhookTextReq.builder().roomId(roomId).text(text).build();
        return this.getWebhookClient().post().contentType(MediaType.APPLICATION_JSON)
                .bodyValue(webhookTextReq).retrieve()
                .bodyToMono(WebhookResp.class);
    }

    public Mono<WebhookResp> replyPdfMono(String roomId, String file_output_name, String msg, String pdfResult) {
        WebhookPdfReq webhookPdfReq = WebhookPdfReq.builder().pdfResult(pdfResult).file_output_name(file_output_name)
                .roomId(roomId).msg(msg).build();
        return this.getWebhookClient().post().contentType(MediaType.APPLICATION_JSON).bodyValue(webhookPdfReq)
                .retrieve().bodyToMono(WebhookResp.class);
    }

    private WebClient getWebhookClient() {
        return WebClient.builder().baseUrl(this.webhookConfig.getUrl())
                .defaultHeaders(headers -> headers.setBearerAuth(this.webhookConfig.getToken()))
                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient)).build();
    }
}
