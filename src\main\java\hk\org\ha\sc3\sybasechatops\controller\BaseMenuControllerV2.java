package hk.org.ha.sc3.sybasechatops.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.config.NotificationConfig;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.SessionAttrEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdGrpTypeEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.AdancedMenu;
import hk.org.ha.sc3.sybasechatops.model.Approval;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheck;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.SimpleMenu;
import hk.org.ha.sc3.sybasechatops.model.Reply;
import hk.org.ha.sc3.sybasechatops.model.db.ChatopsChatroom;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroup;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.repository.GlobalAllowedDBRepository;
import hk.org.ha.sc3.sybasechatops.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import hk.org.ha.sc3.sybasechatops.exception.ChatroomNotFoundException;
import javax.transaction.Transactional; // Consider if still needed with session state

@Slf4j
@RestController
@RequestMapping("v2")
public class BaseMenuControllerV2 {

        private final Reply unknownReply;

        private NotificationConfig notificationConfig;

        private DatabaseRepository databaseRepository;
        private GlobalAllowedDBRepository globalAllowedDBRepository;
        private CommandGroupRepository commandGroupRepository;
        private TokenService tokenService;
        private List<ICommandArgHandler> cmdArgHandlers;

        private static final String BOT_PREFIX = "@itbot|SC3 Database Health Check";
        private static final String PIPE = "\\|";
        private static final int NON_ARGUMENT_SEGMENT_COUNT = 5; // Represents: Bot prefix (2 parts), Session ID/DB type, Command Group ID

        @Value("${gtm.url}")
        private String gtm;

        @Value("${server.port}")
        private int port;

        public BaseMenuControllerV2(NotificationConfig notificationConfig, DatabaseRepository databaseRepository,
                        GlobalAllowedDBRepository globalAllowedDBRepository, CommandGroupRepository commandGroupRepository,
                        TokenService tokenService,
                        List<ICommandArgHandler> cmdArgHandlers) {

                unknownReply = Reply.builder().msg("Unknown option").statusCode(200).build();

                this.notificationConfig = notificationConfig;

                this.databaseRepository = databaseRepository;
                this.globalAllowedDBRepository = globalAllowedDBRepository;
                this.commandGroupRepository = commandGroupRepository;
                this.tokenService = tokenService;
                this.cmdArgHandlers = cmdArgHandlers;

        }

        /**
         * Creates a pattern that matches BOT_PREFIX followed by n number of pipe-separated segments
         * 
         * @param pipeCount number of pipe-separated segments to match
         * @return Pattern matching string with exact number of pipe segments
         */
        private static String createExactPipePattern(int pipeCount) {
                // Always quote the prefix to handle special characters like '|' correctly
                StringBuilder pattern = new StringBuilder(Pattern.quote(BOT_PREFIX));

                if (pipeCount > 0) {
                        for (int i = 0; i < pipeCount; i++) {
                                pattern.append(PIPE).append("[^|]+");
                        }
                }
                return pattern.toString();
        }

        /**
         * Creates a pattern that matches BOT_PREFIX followed by at least n pipe-separated segments
         * 
         * @param minPipes minimum number of pipe-separated segments to match
         * @return Pattern matching string with at least the specified number of pipe segments
         */
        private static String createMinPipePattern(int minPipes) {
                StringBuilder pattern = new StringBuilder(createExactPipePattern(minPipes));
                pattern.append("(?:\\|[^|]+)*"); // Optional additional pipe segments
                return pattern.toString();
        }

        /**
         * Gets the intersection of global allowed database types and team-specific database types
         *
         * @param team the team name
         * @return List of database type strings that are both globally allowed and available for the team
         */
        private List<String> getAvailableDatabaseTypes(String team) {
                // Get global allowed database types
                Set<String> globalAllowedTypes = this.globalAllowedDBRepository.findAll()
                                .stream()
                                .map(globalDb -> globalDb.getDatabaseType().name())
                                .collect(Collectors.toSet());
                
                // Get team-specific database types
                Set<String> teamSpecificTypes = new HashSet<>(this.databaseRepository.findDistinctTypeByTeam(team));
                
                // Return intersection: only types that are both globally allowed AND available for the team
                globalAllowedTypes.retainAll(teamSpecificTypes);
                
                // Convert to sorted list
                return globalAllowedTypes.stream().sorted().collect(Collectors.toList());
        }

        @Transactional
        @PostMapping("menu")
        public ChatopsRespBase baseMenu(@RequestBody ChatopsReq requestBody, HttpSession httpSession)
                        throws JsonMappingException, JsonProcessingException {
                log.debug("Menu entered with session ID: {}", httpSession.getId());
                log.debug(String.format("%-10s: %s", "Env", requestBody.getChatopsEnv()));
                log.debug(String.format("%-10s: %s", "CorpID", requestBody.getCorpID()));
                log.debug(String.format("%-10s: %s", "MsgChain", requestBody.getMsgChain()));
                log.debug(String.format("%-10s: %s", "Keyword (Incoming SessionID)", requestBody.getKeyword())); // Keyword is now incoming session ID
                // Store/Update requesterId and roomId in session from the current request
                // These might be relatively static for a given interaction flow initiated by a user in a room
                httpSession.setAttribute(SessionAttrEnum.REQUESTER_ID.name(), requestBody.getCorpID());
                httpSession.setAttribute(SessionAttrEnum.ROOM_ID.name(), requestBody.getRoomID());

                /*
                 * msg chain sample
                 * 1. @itbot|SC3 Database Health Check|Cloudera|sc3_sand01|Kafka|CLOUDERA_API_STOP|bdvmc2a|Approve|Process
                 * 2. @itbot|SC3 Database Health Check|Cloudera|sc3_sand01|Kafka|CLOUDERA_API_STOP|all|Approve|Process (To Do)
                 * 3. @itbot|SC3 Database Health Check|Cloudera|sc3_sand01|HIVE_ON_TEZ|CLOUDERA_API_CHK_ROLE
                 * 
                 * msgChainArr:
                 * 2. cluster name
                 * 3. service
                 * 4. button name (commandGroupId)
                 * 5. host(s)
                 */

                String msgChain = requestBody.getMsgChain();
                String token = requestBody.getToken();
                String sessionRoomId = Optional.of((String) httpSession.getAttribute(SessionAttrEnum.ROOM_ID.name()))
                                .orElse(requestBody.getRoomID());

                ChatopsChatroom chatopsChatroom = this.tokenService
                                .findChatroomByTokenAndRoomId(token, sessionRoomId)
                                .orElseThrow(() -> new ChatroomNotFoundException(sessionRoomId));

                String[] msgChainArr = msgChain.split("\\|");
                ChatopsRespBase response;

                // Level 1: Handle bot prefix (e.g., "@itbot|SC3 Database Health Check")
                if (msgChain.equals(BOT_PREFIX)) {
                        // Clear any previous state if starting over
                        httpSession.removeAttribute(SessionAttrEnum.DB_TYPE.name());
                        httpSession.setAttribute(SessionAttrEnum.TEAM.name(), chatopsChatroom.getTeam());

                        String sessionId = httpSession.getId(); // Get session ID

                        List<Option> optionsWithSessionId = new ArrayList<>();
                        for (String dbType : getAvailableDatabaseTypes(chatopsChatroom.getTeam())) {

                                Option option = Option.builder().text(dbType) // Updated to use dbType instead of cluster
                                                .value(sessionId + "|" + dbType) // Updated to use dbType instead of cluster
                                                .build();
                                optionsWithSessionId.add(option);
                        }
                        AdancedMenu productMenu = AdancedMenu.builder().msg("Please select a products below")
                                        .options(optionsWithSessionId).statusCode(200).store(null).build();
                        return productMenu;
                }

                // Level 2: Handle "@itbot|SC3 Database Health Check|sessionId" pattern
                if (Pattern.matches(createExactPipePattern(1), msgChain)) {
                        List<String> attributeNames = Collections.list(httpSession.getAttributeNames());
                        for (String attributeName : attributeNames) {
                                httpSession.removeAttribute(attributeName);
                        }
                        httpSession.setAttribute(SessionAttrEnum.REQUESTER_ID.name(), requestBody.getCorpID());
                        httpSession.setAttribute(SessionAttrEnum.ROOM_ID.name(), requestBody.getRoomID());
                        String sessionIdFromMsg = msgChainArr[2];
                        httpSession.setAttribute(SessionAttrEnum.TEAM.name(), chatopsChatroom.getTeam());
                        
                        log.debug("Session ID from msgChain (Level 2): {}", sessionIdFromMsg);
                        
                        // Get database types for the team (union of global allowed and team-specific)
                        List<String> dbTypes = getAvailableDatabaseTypes(chatopsChatroom.getTeam());
                        
                        // Create SimpleMenu with database types as simple string options (no sessionId attachment)
                        SimpleMenu databaseMenu = SimpleMenu.builder()
                                        .msg("Please select a database type below")
                                        .options(dbTypes.toArray(new String[0]))
                                        .statusCode(200)
                                        .store(null)
                                        .build();
                                        
                        return databaseMenu;
                }

                // Level 3: Handle database type selection (e.g., "@itbot|SC3 Database Health Check|sessionId|Cloudera")
                if (Pattern.matches(createExactPipePattern(2), msgChain)) {
                        String sessionIdFromMsg = msgChainArr[2];
                        httpSession.setAttribute(SessionAttrEnum.TEAM.name(), chatopsChatroom.getTeam());

                        log.debug("Session ID from msgChain (Level 2): {}", sessionIdFromMsg);

                        // String dbTypeStr = getDBTypeStr(msgChain);
                        String dbTypeStr = msgChainArr[3];
                        DatabaseTypeEnum databaseType = DatabaseTypeEnum.valueOf(dbTypeStr.toUpperCase());
                        if (databaseType != null) {
                                httpSession.setAttribute(SessionAttrEnum.DB_TYPE.name(), dbTypeStr);
                                response = this.getCommandMenu(databaseType);
                                return response;
                        }
                }

                // Level 3: Handle command argument selection (e.g., "@itbot|SC3 Database Health Check|sessionId|Cloudera|CLOUDERA_API_CHK_ROLE")
                if (Pattern.matches(createMinPipePattern(3), msgChain)) {
                        log.debug("Session ID from msgChain (Level 3): {}", httpSession.getId());

                        String dbTypeFromMsg = msgChainArr[3];
                        httpSession.setAttribute(SessionAttrEnum.DB_TYPE.name(), dbTypeFromMsg);

                        String commandGroupId = msgChainArr[4];
                        httpSession.setAttribute(SessionAttrEnum.CMD_GRP_ID.name(), commandGroupId);

                        Optional<CommandGroup> commandGroupOptional = this.commandGroupRepository
                                        .findById(commandGroupId);

                        if (!commandGroupOptional.isPresent()) {
                                log.warn("Unknown command group ID: {}", commandGroupId);
                                response = this.unknownReply;
                                return response;
                        }

                        CommandGroup commandGroup = commandGroupOptional.get();
                        int argSize = commandGroup.getArguments().size();

                        // Set argument in msgchain to httpsession object dynamicaly
                        // e.g. @itbot|SC3 Database Health Check|99ec1eba-0c4e-4175-9b34-25062852d7d9|CLOUDERA|CLOUDERA_API_CHK_ROLE|ARG_CDP_CLUSTER
                        // Get the json string value from the keyword field and set it to the session
                        String keywordJsonStr = requestBody.getKeyword();
                        if (!keywordJsonStr.equals("{keyword}")) {
                                // loop through the JsonString and set the session
                                ObjectMapper objectMapper = new ObjectMapper();
                                Map<String, String> keywordJson = objectMapper.readValue(keywordJsonStr, Map.class);
                                // Loop through the map and set session attributes
                                keywordJson.forEach(httpSession::setAttribute);
                        }

                        // When all arguments are provided, create health check keyword
                        if (msgChainArr.length < argSize + NON_ARGUMENT_SEGMENT_COUNT) {

                                // prompt argument menu depend on the index of msgchain
                                int argIdx = msgChainArr.length - NON_ARGUMENT_SEGMENT_COUNT;
                                CommandArgument commandArgument = commandGroup.getArguments().get(argIdx);

                                for (ICommandArgHandler cmdArgHandler : this.cmdArgHandlers) {
                                        if (cmdArgHandler.getCmdArgType().equals(commandArgument.getArgType())) {
                                                // cmdArgHandler now gets session ID to put in 'nextKeyword' of its response
                                                response = cmdArgHandler.getChatopsResp(commandArgument, httpSession,
                                                                msgChain);
                                                return response;
                                        }
                                }
                        }

                        // Handle approval processing: "|Approve|Process" pattern
                        if (msgChain.endsWith("|Approve|Process")) {
                                response = this.postApprovedHealthCheckAction(httpSession.getId(), token,
                                                "Approved Health Check Request");
                                return response;
                        }

                        // All arguments collected - check if approval is required
                        if (commandGroup.isApproval()) {
                                // Return approval request
                                response = Approval.builder().keyword("approval").statusCode(200)
                                                .msg("Hi, APPROVERS. Please approve the request by REQUESTER.")
                                                .store(null).build();
                                return response;
                        }

                        // Normal execution for non-approval commands
                        response = this.postHealthCheckAction(httpSession.getId(), token);
                        return response;

                }

                log.warn("Unknown msgChain pattern: {}", msgChain);
                response = this.unknownReply;
                return response;
        }

        private String getHealthCheckUrl() {
                String url = this.gtm + ":" + this.port + "/chatops/v2/health_check";
                return url;
        }

        private ChatopsRespBase postHealthCheckAction(String keyword, String token) {
                HealthCheck dbHealthCheck = HealthCheck.builder().keyword(keyword)
                                .msg("Health check processing (KEYWORD)").url(getHealthCheckUrl()).statusCode(200)
                                .store(null).token(token).build();
                return dbHealthCheck;
        }

        private ChatopsRespBase postApprovedHealthCheckAction(String keyword, String token, String shortDesc) {
                HealthCheck dbHealthCheck = HealthCheck.builder().keyword(keyword)
                                .msg("Approved Health check processing (KEYWORD)").url(getHealthCheckUrl()).statusCode(200)
                                .token(token).notify(this.notificationConfig.getEmail()).shortDesc(shortDesc)
                                .store(null).build();
                return dbHealthCheck;
        }

        private SimpleMenu getCommandMenu(DatabaseTypeEnum dbType) {
                List<CmdGrpTypeEnum> cmdTypes = new ArrayList<>(Arrays.asList(CmdGrpTypeEnum.values()));
                return this.getCommandMenu(dbType, cmdTypes);
        }

        private SimpleMenu getCommandMenu(DatabaseTypeEnum dbType, List<CmdGrpTypeEnum> cmdTypes) {
                List<CommandGroup> commandGroups = commandGroupRepository.findByDbTypeAndCmdTypeInAndEnabledTrue(dbType,
                                cmdTypes);
                /* use stream to get commands' id to array */
                SimpleMenu cmdMenu = SimpleMenu.builder().msg("Please select a command below")
                                .options(commandGroups.stream().map(CommandGroup::getId).toArray(String[]::new))
                                .statusCode(200).store(null).build();
                return cmdMenu;
        }

}