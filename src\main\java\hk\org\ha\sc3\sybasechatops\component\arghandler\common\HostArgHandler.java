package hk.org.ha.sc3.sybasechatops.component.arghandler.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.SessionAttrEnum;
import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class HostArgHandler extends ICommandArgHandler {

    private DatabaseRepository databaseRepository;

    public HostArgHandler(DatabaseRepository databaseRepository) {
        this.databaseRepository = databaseRepository;
    }

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_HOST;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        // Extract values from HttpSession
        String team = (String) httpSession.getAttribute(SessionAttrEnum.TEAM.name());
        String dbTypeStr = (String) httpSession.getAttribute(SessionAttrEnum.DB_TYPE.name());

        DatabaseTypeEnum dbType = dbTypeStr != null
                ? DatabaseTypeEnum.valueOf(dbTypeStr.toUpperCase())
                : null;
                
        if (team != null) {
            for (String host : databaseRepository.findDistinctHostByTeamAndType(team, dbType)) {
                hiddenValue.put(getCmdArgType().name(), host);
                try {
                    Option option = Option.builder()
                        .text(host)
                        .value(mapper.writeValueAsString(hiddenValue))
                        .nextLabel(msgChain + "|" + getCmdArgType())
                        .build();
                    options.add(option);
                } catch (JsonProcessingException e) {
                    log.error("Error processing JSON for host: {}", host, e);
                }
            }
        }

        return options;
    }
}
